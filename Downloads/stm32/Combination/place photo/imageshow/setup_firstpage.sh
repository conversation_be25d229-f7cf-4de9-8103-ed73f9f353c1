#!/bin/bash

echo "🚀 STM32 FirstPage 快速设置"
echo "================================"
echo

# 检查是否存在first page.png文件
if [ ! -f "../../first page.png" ]; then
    echo "❌ 错误: 找不到 first page.png"
    echo "   请确保文件在 Downloads/Combination/ 目录中"
    echo
    read -p "按回车键退出..."
    exit 1
fi

echo "✅ 找到 first page.png 文件"
echo

# 提示用户插入SD卡
echo "📱 请插入SD卡并记下挂载点"
echo "   常见位置: /Volumes/SDCARD, /media/sdcard 等"
echo
read -p "输入SD卡挂载点 (例如: /Volumes/SDCARD): " mount_point

# 验证挂载点是否存在
if [ ! -d "$mount_point" ]; then
    echo "❌ 错误: 挂载点 $mount_point 不存在"
    echo "   请检查SD卡是否正确挂载"
    echo
    read -p "按回车键退出..."
    exit 1
fi

echo
echo "🔍 检查SD卡结构..."

# 创建PICTURE目录（如果不存在）
if [ ! -d "$mount_point/PICTURE" ]; then
    echo "📁 创建 PICTURE 目录..."
    mkdir "$mount_point/PICTURE"
    if [ $? -ne 0 ]; then
        echo "❌ 错误: 无法创建 PICTURE 目录"
        echo "   请检查SD卡权限"
        echo
        read -p "按回车键退出..."
        exit 1
    fi
    echo "✅ PICTURE 目录创建成功"
else
    echo "✅ PICTURE 目录已存在"
fi

echo
echo "📋 复制 first page.png 到SD卡..."

# 复制文件
cp "../../first page.png" "$mount_point/PICTURE/first page.png"
if [ $? -ne 0 ]; then
    echo "❌ 错误: 复制文件失败"
    echo "   请检查:"
    echo "   - SD卡空间是否足够"
    echo "   - SD卡是否写保护"
    echo "   - 文件是否被其他程序占用"
    echo
    read -p "按回车键退出..."
    exit 1
fi

echo
echo "🎉 设置完成!"
echo "================================"
echo
echo "✅ first page.png 已复制到:"
echo "   $mount_point/PICTURE/first page.png"
echo
echo "📋 接下来:"
echo "   1. 安全弹出SD卡"
echo "   2. 插入STM32设备"
echo "   3. 开机即可看到firstpage"
echo
echo "💡 提示: 确保STM32固件已包含firstpage显示功能"
echo
read -p "按回车键退出..."
