@echo off
echo ========================================
echo STM32 FirstPage Setup Script
echo ========================================
echo.

REM 检查是否存在first page.png文件
if not exist "..\..\first page.png" (
    echo Error: first page.png not found in Downloads/Combination/
    echo Please make sure the file exists in the correct location.
    echo.
    pause
    exit /b 1
)

echo Found first page.png file.
echo.

REM 提示用户插入SD卡
echo Please insert your SD card and note the drive letter.
echo Example: E:, F:, G:, etc.
echo.
set /p drive_letter=Enter SD card drive letter (e.g., E): 

REM 验证驱动器是否存在
if not exist "%drive_letter%:\" (
    echo Error: Drive %drive_letter%: not found.
    echo Please check if the SD card is properly inserted.
    echo.
    pause
    exit /b 1
)

echo.
echo Checking SD card structure...

REM 创建PICTURE目录（如果不存在）
if not exist "%drive_letter%:\PICTURE" (
    echo Creating PICTURE directory...
    mkdir "%drive_letter%:\PICTURE"
    if errorlevel 1 (
        echo Error: Failed to create PICTURE directory.
        echo Please check SD card permissions.
        echo.
        pause
        exit /b 1
    )
    echo PICTURE directory created successfully.
) else (
    echo PICTURE directory already exists.
)

echo.
echo Copying first page.png to SD card...

REM 复制文件
copy "..\..\first page.png" "%drive_letter%:\PICTURE\first page.png"
if errorlevel 1 (
    echo Error: Failed to copy first page.png to SD card.
    echo Please check:
    echo - SD card has enough space
    echo - SD card is not write-protected
    echo - File is not in use by another program
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo The first page.png has been copied to:
echo %drive_letter%:\PICTURE\first page.png
echo.
echo You can now:
echo 1. Safely eject the SD card
echo 2. Insert it into your STM32 device
echo 3. Power on the device to see the firstpage
echo.
echo Note: Make sure your STM32 firmware includes the
echo firstpage display modifications.
echo.
pause
