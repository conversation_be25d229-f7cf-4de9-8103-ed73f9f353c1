#include "system.h"
#include "SysTick.h"
#include "led.h"
#include "usart.h"
#include "key.h"
#include "tftlcd.h" 
#include "sram.h"
#include "malloc.h" 
#include "sdio_sdcard.h" 
#include "flash.h"
#include "ff.h" 
#include "fatfs_app.h"
#include "font_show.h"
#include "piclib.h"
#include "string.h"		
#include "math.h"
#include "touch.h"
#include "gui.h"
#include "button.h"

// 页面状态定义
#define PAGE_FIRST 0
#define PAGE_SENSOR 1

// 全局变量
u8 current_page = PAGE_FIRST;
_btn_obj *data_detection_btn = NULL;

// 函数声明
void display_first_page(void);
void display_sensor_page(void);
void setup_touch_areas(void);
void handle_touch_input(void);

// 显示第一页
void display_first_page(void)
{
	LCD_Clear(BLACK);
	if(f_stat("0:/PICTURE/first page.jpg", NULL) == FR_OK) {
		picfile_display("0:/PICTURE/first page.jpg", 0, 0, tftlcd_data.width, tftlcd_data.height, 1);
		current_page = PAGE_FIRST;
	} else {
		FRONT_COLOR = WHITE;
		LCD_ShowString(10, 90, tftlcd_data.width, tftlcd_data.height, 16, "first page.jpg not found!");
	}
}

// 显示传感器页面
void display_sensor_page(void)
{
	LCD_Clear(BLACK);
	if(f_stat("0:/PICTURE/Sensor page.jpg", NULL) == FR_OK) {
		picfile_display("0:/PICTURE/Sensor page.jpg", 0, 0, tftlcd_data.width, tftlcd_data.height, 1);
		current_page = PAGE_SENSOR;
	} else {
		FRONT_COLOR = WHITE;
		LCD_ShowString(10, 90, tftlcd_data.width, tftlcd_data.height, 16, "Sensor page.jpg not found!");
	}
}

// 设置触摸区域 - 假设Data Detection按钮在屏幕右下角区域
void setup_touch_areas(void)
{
	// 创建Data Detection按钮触摸区域
	// 假设按钮位置在屏幕右下角，大小约为120x60像素
	u16 btn_width = 120;
	u16 btn_height = 60;
	u16 btn_x = tftlcd_data.width - btn_width - 20;  // 距离右边缘20像素
	u16 btn_y = tftlcd_data.height - btn_height - 20; // 距离底边20像素
	
	data_detection_btn = btn_creat(btn_x, btn_y, btn_width, btn_height, 0, BTN_TYPE_TEXTA);
	if(data_detection_btn != NULL) {
		data_detection_btn->caption = (u8*)"Data Detection";
		data_detection_btn->font = 16;
	}
}

// 处理触摸输入
void handle_touch_input(void)
{
	if(tp_dev.scan(0)) {  // 检测到触摸
		in_obj.intype = IN_TYPE_TOUCH;
		in_obj.x = tp_dev.x[0];
		in_obj.y = tp_dev.y[0];
		in_obj.ksta = (tp_dev.sta & TP_PRES_DOWN) ? 1 : 0;
		
		// 检查是否触摸了Data Detection按钮区域
		if(current_page == PAGE_FIRST && data_detection_btn != NULL) {
			if(btn_check(data_detection_btn, &in_obj)) {
				display_sensor_page();
				delay_ms(200); // 防抖动
			}
		}
		// 如果在传感器页面，任意触摸返回第一页
		else if(current_page == PAGE_SENSOR) {
			display_first_page();
			delay_ms(200); // 防抖动
		}
	}
}

int main()
{
	u8 i=0;
	u8 key;
	u8 res;
 	DIR picdir;	 		//ͼƬĿ¼
	FILINFO *picfileinfo;//�ļ���Ϣ
	u8 *pname;			//��·�����ļ���
	u16 totpicnum; 		//ͼƬ�ļ�����
	u16 curindex;		//ͼƬ��ǰ����
	u8 pause=0;			//��ͣ���
	u16 temp;
	u32 *picoffsettbl;	//ͼƬ�ļ�offset������
	u16 pic_height;		//图片区域高度
	u16 text_start_y;	//文字开始位置
	
	SysTick_Init(168);
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);  //�ж����ȼ����� ��2��
	LED_Init();
	USART1_Init(115200);
	TFTLCD_Init();			//LCD��ʼ��
	KEY_Init();
	TP_Init();				//触摸屏初始化
	gui_init();				//GUI初始化
	EN25QXX_Init();					 
	my_mem_init(SRAMIN);		//��ʼ���ڲ��ڴ��
	
	FRONT_COLOR=RED;
	while(SD_Init())//��ⲻ��SD��
	{
		LCD_ShowString(10,10,tftlcd_data.width,tftlcd_data.height,16,"SD Card Error!");
		delay_ms(500);
		LED2=!LED2;
	}
	
	FATFS_Init();				//Ϊfatfs��ر��������ڴ�				 
  	f_mount(fs[0],"0:",1); 		//����SD��
	f_mount(fs[1],"1:",1); 		//����SPI FLASH
	while(font_init()) 		        //����ֿ�
	{  
		LCD_ShowString(10,10,tftlcd_data.width,tftlcd_data.height,16,"Font Error!   ");
		delay_ms(500);
	} 
	LCD_ShowFontString(10,10,tftlcd_data.width,tftlcd_data.height,"���пƼ�-PRECHIN",16,0);
	LCD_ShowFontString(10,30,tftlcd_data.width,tftlcd_data.height,"www.prechin.net",16,0);
	LCD_ShowFontString(10,50,tftlcd_data.width,tftlcd_data.height,"�������-ͼƬ��ʾ",16,0);
	
	while(f_opendir(&picdir,"0:/PICTURE"))//��ͼƬ�ļ���
 	{	    
		LCD_ShowFontString(10,70,tftlcd_data.width,tftlcd_data.height,"PICTURE�ļ��д���!",16,0);
		delay_ms(200);
		LCD_Fill(10,70,tftlcd_data.width,86,WHITE);//�����ʾ
		delay_ms(200);
	}  
	totpicnum=fatfs_get_filetype_tnum("0:/PICTURE",TYPE_PICTURE); //�õ�����Ч�ļ���
  	while(totpicnum==NULL)//ͼƬ�ļ�Ϊ0		
 	{	    
		LCD_ShowFontString(10,70,tftlcd_data.width,tftlcd_data.height,"û��ͼƬ�ļ�!",16,0);
		delay_ms(200);
		LCD_Fill(10,70,tftlcd_data.width,86,WHITE);//�����ʾ
		delay_ms(200);		
	} 
	picfileinfo=(FILINFO*)mymalloc(SRAMIN,sizeof(FILINFO));	//�����ڴ�
 	pname=mymalloc(SRAMIN,_MAX_LFN*2+1);	//Ϊ��·�����ļ��������ڴ�
 	picoffsettbl=mymalloc(SRAMIN,4*totpicnum);	//����4*totpicnum���ֽڵ��ڴ�,���ڴ��ͼƬ����
 	while(!picfileinfo||!pname||!picoffsettbl)	//�ڴ�������
 	{	    	
		LCD_ShowFontString(10,70,tftlcd_data.width,tftlcd_data.height,"�ڴ����ʧ��!",16,0);
		delay_ms(200);				  
		LCD_Fill(10,70,tftlcd_data.width,86,WHITE);//�����ʾ
		delay_ms(200);				  
	}  	
	//��¼����
    res=f_opendir(&picdir,"0:/PICTURE"); //��Ŀ¼
	if(res==FR_OK)
	{
		curindex=0;//��ǰ����Ϊ0
		while(1)//ȫ����ѯһ��
		{
			temp=picdir.dptr;								//��¼��ǰdptrƫ��
	        res=f_readdir(&picdir,picfileinfo);       		//��ȡĿ¼�µ�һ���ļ�
	        if(res!=FR_OK||picfileinfo->fname[0]==0)break;	//������/��ĩβ��,�˳�	 	 
			res=f_typetell((u8*)picfileinfo->fname);	
			if((res&0XF0)==TYPE_PICTURE)//ȡ����λ,�����ǲ���ͼƬ�ļ�	
			{
				picoffsettbl[curindex]=temp;//��¼����
				curindex++;
			}	    
		} 
	}   
	LCD_ShowFontString(10,70,tftlcd_data.width,tftlcd_data.height,"��ʼ��ʾ...",16,0);
	delay_ms(1500);
	piclib_init();										//��ʼ����ͼ

	// 初始化触摸区域
	setup_touch_areas();
	
	// 显示第一页
	display_first_page();
	
	// 主循环 - 处理触摸输入
	while(1) {
		handle_touch_input();
		
		// LED闪烁指示系统运行
		if(i++ % 100 == 0) {
			LED1 = !LED1;
		}
		
		delay_ms(10);
	}

	// 清理内存
	myfree(SRAMIN,picfileinfo);			//�ͷ��ڴ�						   		    
	myfree(SRAMIN,pname);				//�ͷ��ڴ�			    
	myfree(SRAMIN,picoffsettbl);			//�ͷ��ڴ�
	
	// 清理按钮对象
	if(data_detection_btn != NULL) {
		btn_delete(data_detection_btn);
	}
}
