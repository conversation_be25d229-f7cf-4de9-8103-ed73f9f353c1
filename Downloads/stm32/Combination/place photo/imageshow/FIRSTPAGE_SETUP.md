# STM32 FirstPage 开机显示 - 快速设置指南

## 🚀 快速开始

### 1. 准备SD卡
1. 将SD卡格式化为 **FAT32** 格式
2. 在SD卡根目录创建 `PICTURE` 文件夹
3. 将 `first page.png` 复制到 `PICTURE` 文件夹中

### 2. SD卡目录结构
```
SD卡根目录/
└── PICTURE/
    ├── first page.png  ← 这是开机显示的图片
    └── 其他图片文件...
```

### 3. 烧录和测试
1. 用Keil打开 `Template.uvprojx` 项目
2. 编译项目 (Build → Rebuild All)
3. 烧录到STM32
4. 插入准备好的SD卡
5. 重启设备

## 📱 使用说明

### 开机流程：
1. **系统初始化** → LCD、SD卡、字库等
2. **检查firstpage** → 查找 `PICTURE/first page.png`
3. **显示firstpage** → 如果找到就显示，LED闪烁提示
4. **等待按键** → 按任意键继续到图片浏览
5. **图片浏览** → 自动播放所有图片

### 按键操作：
- **KEY_UP** ⬆️ : 上一张图片
- **KEY1** ➡️ : 下一张图片
- **KEY0** ⏸️ : 暂停/继续自动播放

### LED指示：
- **闪烁** 💡 : 等待按键继续
- **常亮** 🔆 : 正常运行
- **熄灭** ⚫ : 系统空闲

## ⚠️ 重要注意事项

### 文件要求：
- **文件名**: 必须是 `first page.png` (注意空格)
- **格式**: PNG、JPG、BMP都支持
- **位置**: 必须在SD卡的 `PICTURE` 文件夹中
- **大小**: 建议不超过1MB，避免加载过慢

### SD卡要求：
- **格式**: FAT32格式
- **容量**: 建议8GB以下，兼容性更好
- **速度**: Class 4以上

## 🔧 故障排除

| 问题 | 解决方法 |
|------|----------|
| firstpage不显示 | 检查文件名是否正确，是否在PICTURE文件夹中 |
| 显示花屏 | 检查图片格式，尝试转换为PNG格式 |
| SD卡读取失败 | 重新格式化SD卡为FAT32 |
| 按键无响应 | 检查硬件连接 |

## 🎯 完成！

现在你的STM32将在开机时：
1. ✅ 显示 firstpage 图片
2. ✅ LED闪烁提示用户
3. ✅ 等待按键继续
4. ✅ 进入正常图片浏览模式

**享受你的Mars Environment Simulation System！** 🚀
