/*******************************************************************************
*
*                 		       DHT22温湿度显示实验
--------------------------------------------------------------------------------
* 实 验 名		 : DHT22温湿度传感器LCD显示实验
* 实验说明       : 读取DHT22温湿度数据并在LCD屏幕上显示
* 连接方式       : DHT22数据线连接到PA9
* 注    意		 : DHT22库文件已适配标准库
*******************************************************************************/

#include "system.h"
#include "SysTick.h"
#include "led.h"
#include "usart.h"
#include "tftlcd.h"
#include "picture.h"
#include "DHT22.h"
#include "BH1750.h"
#include "WindSpeed.h"
#include "logo.h"
#include <stdio.h>

// Test function declarations (commented out to avoid errors)
// void Simple_WindSpeed_Test(void);
// void Simple_ADC_Test(void);
// void LCD_WindSpeed_Test(void);
// void Simple_LCD_ADC_Test(void);


/*******************************************************************************
* �� �� ��         : main
* ��������		   : ������
* ��    ��         : ��
* ��    ��         : ��
*******************************************************************************/
int main()
{
	u8 i=0;
	float temperature = 0.0f;
	float humidity = 0.0f;
	float light_intensity = 0.0f;
	float wind_speed = 0.0f;
	char temp_str[20];
	char humi_str[20];
	char light_str[20];
	char wind_str[20];
	char debug_str[60];
	bool dht22_status = false;
	u16 read_count = 0;
	u16 error_count = 0;

	SysTick_Init(168);
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);  //中断优先级分组 组2
	LED_Init();
	USART1_Init(115200);
	TFTLCD_Init();			//LCD初始化
	DHT22_Init();			//DHT22初始化
	BH1750_Init();			//BH1750光照传感器初始化
	WindSpeed_Init();		//风速传感器初始化

	// 清屏并设置背景色
	LCD_Clear(WHITE);
	FRONT_COLOR=BLACK;

	// 显示标题 - 使用更大字体
	LCD_ShowString(10, 10, tftlcd_data.width, tftlcd_data.height, 24, "Environment Monitor");
	LCD_ShowString(10, 40, tftlcd_data.width, tftlcd_data.height, 16, "By Michigear");

	// 显示固定标签 - 使用更大字体和调整间距
	LCD_ShowString(10, 80, tftlcd_data.width, tftlcd_data.height, 24, "Temp:");
	LCD_ShowString(10, 110, tftlcd_data.width, tftlcd_data.height, 24, "Humi:");
	LCD_ShowString(10, 140, tftlcd_data.width, tftlcd_data.height, 24, "Light:");
	LCD_ShowString(10, 170, tftlcd_data.width, tftlcd_data.height, 24, "Wind:");
	LCD_ShowString(10, 200, tftlcd_data.width, tftlcd_data.height, 24, "Status:");

	// 添加调试信息显示区域 - 调整位置
	LCD_ShowString(10, 240, tftlcd_data.width, tftlcd_data.height, 16, "Debug Info:");
	LCD_ShowString(10, 260, tftlcd_data.width, tftlcd_data.height, 16, "System Started");

	// 显示统计信息标签 - 调整位置
	LCD_ShowString(10, 290, tftlcd_data.width, tftlcd_data.height, 16, "Reads:");
	LCD_ShowString(10, 310, tftlcd_data.width, tftlcd_data.height, 16, "Errors:");

	printf("DHT22 Temperature & Humidity Monitor Started\r\n");

	// Uncomment one of these lines to run tests:
	// Simple_LCD_ADC_Test();    // Simple ADC test on LCD
	// LCD_WindSpeed_Test();     // Wind sensor only test
	// All_Sensors_LCD_Test();   // All sensors test (like normal system)

	// Or comment all above to run normal system

	while(1)
	{
		i++;

		// 每2秒读取一次传感器数据
		if(i % 200 == 0)
		{
			read_count++;
			dht22_status = DHT22_GetTemp_Humidity(&temperature, &humidity);
			light_intensity = LIght_Intensity();  // 读取光照强度
			wind_speed = WindSpeed_GetSpeedFiltered();  // 读取风速

			if(dht22_status)
			{
				// 格式化字符串
				sprintf(temp_str, "%.1f C  ", temperature);
				sprintf(humi_str, "%.1f %%  ", humidity);
				sprintf(light_str, "%.1f lx  ", light_intensity);
				sprintf(wind_str, "%.1f m/s  ", wind_speed);

				// 清除之前的数据显示区域 - 调整清除区域位置
				LCD_Fill(120, 80, 300, 105, WHITE);   // 温度
				LCD_Fill(120, 110, 300, 135, WHITE);   // 湿度
				LCD_Fill(120, 140, 300, 165, WHITE);  // 光照
				LCD_Fill(120, 170, 300, 195, WHITE); // 风速
				LCD_Fill(120, 200, 300, 225, WHITE); // 状态
				LCD_Fill(10, 260, 300, 280, WHITE); // 调试信息

				// 显示新的传感器数据 - 使用更大字体和调整位置
				FRONT_COLOR = BLUE;
				LCD_ShowString(120, 80, tftlcd_data.width, tftlcd_data.height, 24, (u8*)temp_str);
				LCD_ShowString(120, 110, tftlcd_data.width, tftlcd_data.height, 24, (u8*)humi_str);
				LCD_ShowString(120, 140, tftlcd_data.width, tftlcd_data.height, 24, (u8*)light_str);
				LCD_ShowString(120, 170, tftlcd_data.width, tftlcd_data.height, 24, (u8*)wind_str);

				// 显示状态
				FRONT_COLOR = GREEN;
				LCD_ShowString(120, 200, tftlcd_data.width, tftlcd_data.height, 24, "OK    ");

				// 显示调试信息到LCD - 调整位置
				sprintf(debug_str, "All OK: T=%.1f H=%.1f L=%.1f W=%.1f", temperature, humidity, light_intensity, wind_speed);
				FRONT_COLOR = BLACK;
				LCD_ShowString(10, 260, tftlcd_data.width, tftlcd_data.height, 16, (u8*)debug_str);

				// 串口输出
				printf("Temp: %.1fC, Humidity: %.1f%%, Light: %.1flx, Wind: %.1fm/s\r\n", temperature, humidity, light_intensity, wind_speed);
			}
			else
			{
				error_count++;

				// DHT22错误但仍显示光照和风速数据
				sprintf(light_str, "%.1f lx  ", light_intensity);
				sprintf(wind_str, "%.1f m/s  ", wind_speed);

				// 清除数据显示区域 - 调整清除区域位置
				LCD_Fill(120, 80, 300, 105, WHITE);   // 温度
				LCD_Fill(120, 110, 300, 135, WHITE);   // 湿度
				LCD_Fill(120, 140, 300, 165, WHITE);  // 光照
				LCD_Fill(120, 170, 300, 195, WHITE); // 风速
				LCD_Fill(120, 200, 300, 225, WHITE); // 状态
				LCD_Fill(10, 260, 300, 280, WHITE); // 调试信息

				// 显示错误信息和其他传感器数据 - 使用更大字体和调整位置
				FRONT_COLOR = RED;
				LCD_ShowString(120, 80, tftlcd_data.width, tftlcd_data.height, 24, "Error ");
				LCD_ShowString(120, 110, tftlcd_data.width, tftlcd_data.height, 24, "Error ");

				// 光照和风速数据正常显示
				FRONT_COLOR = BLUE;
				LCD_ShowString(120, 140, tftlcd_data.width, tftlcd_data.height, 24, (u8*)light_str);
				LCD_ShowString(120, 170, tftlcd_data.width, tftlcd_data.height, 24, (u8*)wind_str);

				// 状态显示
				FRONT_COLOR = RED;
				LCD_ShowString(120, 200, tftlcd_data.width, tftlcd_data.height, 24, "DHT22 Err");

				// 显示错误调试信息到LCD - 调整位置
				sprintf(debug_str, "DHT22 Error! L: %.1flx W: %.1fm/s", light_intensity, wind_speed);
				FRONT_COLOR = RED;
				LCD_ShowString(10, 260, tftlcd_data.width, tftlcd_data.height, 16, (u8*)debug_str);

				printf("DHT22 Read Error! Light: %.1flx, Wind: %.1fm/s\r\n", light_intensity, wind_speed);
			}

			// 更新统计信息 - 调整位置和字体大小
			LCD_Fill(120, 290, 250, 330, WHITE);  // 清除统计信息区域
			sprintf(debug_str, "%d", read_count);
			FRONT_COLOR = BLACK;
			LCD_ShowString(120, 290, tftlcd_data.width, tftlcd_data.height, 16, (u8*)debug_str);

			sprintf(debug_str, "%d", error_count);
			if(error_count > 0)
				FRONT_COLOR = RED;
			else
				FRONT_COLOR = BLACK;
			LCD_ShowString(120, 310, tftlcd_data.width, tftlcd_data.height, 16, (u8*)debug_str);
		}

		// LED闪烁指示系统运行
		if(i % 50 == 0)
		{
			LED1=!LED1;
		}

		delay_ms(10);
	}
}


