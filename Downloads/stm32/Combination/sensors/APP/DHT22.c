/*
Library:					DHT22 - AM2302 Temperature and <PERSON><PERSON><PERSON><PERSON> Sensor
Written by:				<PERSON> (M<PERSON><PERSON>oobEmbedded YouTube Channel)
Date Written:			21/11/2018
Last modified:		Adapted for STM32F4xx Standard Library
Description:			This is an STM32 device driver library for the DHT22 Temperature and Humidity Sensor, using STM32F4xx Standard Library
*/

//Header files
#include "DHT22.h"

//Bit fields manipulations
#define bitRead(value, bit) (((value) >> (bit)) & 0x01)
#define bitSet(value, bit) ((value) |= (1UL << (bit)))
#define bitClear(value, bit) ((value) &= ~(1UL << (bit)))
#define bitWrite(value, bit, bitvalue) (bitvalue ? bitSet(value, bit) : bitClear(value, bit))

//DHT22初始化
void DHT22_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	
	// 使能GPIOA时钟
	RCC_AHB1PeriphClockCmd(DHT22_GPIO_CLK, ENABLE);
	
	// 配置PA9为输出模式
	GPIO_InitStructure.GPIO_Pin = DHT22_GPIO_PIN;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(DHT22_GPIO_PORT, &GPIO_InitStructure);
	
	// 初始状态设为高电平
	DHT22_Pin_Write(1);
}

//改变引脚模式
static void DHT22_PinMode(DHT22_PinMode_Typedef mode)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	
	GPIO_InitStructure.GPIO_Pin = DHT22_GPIO_PIN;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	
	if(mode == DHT22_OUTPUT)
	{
		GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
		GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
		GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	}
	else if(mode == DHT22_INPUT)
	{
		GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
		GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	}
	
	GPIO_Init(DHT22_GPIO_PORT, &GPIO_InitStructure);
}

//DHT22引脚写入
static void DHT22_Pin_Write(bool state)
{
	if(state) 
		PAout(DHT22_PIN_NUM) = 1;
	else 
		PAout(DHT22_PIN_NUM) = 0;
}

//DHT22引脚读取
static bool DHT22_Pin_Read(void)
{
	return (bool)PAin(DHT22_PIN_NUM);
}

//微秒延时
static void DHT22_DelayMicroSeconds(uint32_t uSec)
{
	uint32_t uSecVar = uSec;
	// 使用168MHz系统时钟进行微秒延时
	uSecVar = uSecVar * (168000000/1000000)/3;
	while(uSecVar--);
}

//DHT22开始采集
static void DHT22_StartAcquisition(void)
{
	//设置数据引脚为输出模式
	DHT22_PinMode(DHT22_OUTPUT);
	//拉低引脚
	DHT22_Pin_Write(0);
	//延时500微秒
	DHT22_DelayMicroSeconds(500);
	//拉高引脚
	DHT22_Pin_Write(1);
	//延时30微秒
	DHT22_DelayMicroSeconds(30);
	//设置引脚为输入模式
	DHT22_PinMode(DHT22_INPUT);
}

//读取5字节原始数据
static void DHT22_ReadRaw(uint8_t *data)
{
	uint32_t rawBits = 0UL;
	uint8_t checksumBits = 0;
	int8_t i;

	DHT22_DelayMicroSeconds(40);
	while(!DHT22_Pin_Read());
	while(DHT22_Pin_Read());

	for(i=31; i>=0; i--)
	{
		while(!DHT22_Pin_Read());
		DHT22_DelayMicroSeconds(40);
		if(DHT22_Pin_Read())
		{
			rawBits |= (1UL << i);
		}
		while(DHT22_Pin_Read());
	}

	for(i=7; i>=0; i--)
	{
		while(!DHT22_Pin_Read());
		DHT22_DelayMicroSeconds(40);
		if(DHT22_Pin_Read())
		{
			checksumBits |= (1UL << i);
		}
		while(DHT22_Pin_Read());
	}

	//复制原始数据到字节数组
	data[0] = (rawBits>>24)&0xFF;
	data[1] = (rawBits>>16)&0xFF;
	data[2] = (rawBits>>8)&0xFF;
	data[3] = (rawBits>>0)&0xFF;
	data[4] = (checksumBits)&0xFF;
}

//获取温湿度数据
bool DHT22_GetTemp_Humidity(float *Temp, float *Humidity)
{
	uint8_t dataArray[6], myChecksum;
	uint16_t Temp16, Humid16;
	uint8_t k;

	//开始数据采集
	DHT22_StartAcquisition();
	//获取原始数据
	DHT22_ReadRaw(dataArray);
	//计算校验和
	myChecksum = 0;
	for(k=0; k<4; k++)
	{
		myChecksum += dataArray[k];
	}

	if(myChecksum == dataArray[4])
	{
		Temp16 = (dataArray[2] <<8) | dataArray[3];
		Humid16 = (dataArray[0] <<8) | dataArray[1];

		*Temp = Temp16/10.0f;
		*Humidity = Humid16/10.0f;
		return 1;
	}
	return 0;
}
