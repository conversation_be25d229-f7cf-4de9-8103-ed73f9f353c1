/**
  ******************************************************************************
  * @file    WindSpeed.c
  * <AUTHOR> Name
  * @brief   ZTS-3000-FSJT-V05 风速传感器驱动实现文件
  ******************************************************************************
  */

#include "WindSpeed.h"
#include "SysTick.h"

/**
  * @brief  风速传感器初始化
  * @param  None
  * @retval None
  */
void WindSpeed_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    ADC_InitTypeDef ADC_InitStructure;
    ADC_CommonInitTypeDef ADC_CommonInitStructure;
    
    // 使能GPIO和ADC时钟
    RCC_AHB1PeriphClockCmd(WINDSPEED_ADC_CLK, ENABLE);
    RCC_APB2PeriphClockCmd(WINDSPEED_ADC_CLK_CMD, ENABLE);
    
    // 配置GPIO为模拟输入模式
    GPIO_InitStructure.GPIO_Pin = WINDSPEED_ADC_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(WINDSPEED_ADC_PORT, &GPIO_InitStructure);
    
    // ADC通用配置
    ADC_CommonInitStructure.ADC_Mode = ADC_Mode_Independent;
    ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div4;
    ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_Disabled;
    ADC_CommonInitStructure.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_5Cycles;
    ADC_CommonInit(&ADC_CommonInitStructure);
    
    // ADC配置
    ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;
    ADC_InitStructure.ADC_ScanConvMode = DISABLE;
    ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;
    ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_None;
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T1_CC1;
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
    ADC_InitStructure.ADC_NbrOfConversion = 1;
    ADC_Init(WINDSPEED_ADC, &ADC_InitStructure);
    
    // 配置ADC通道
    ADC_RegularChannelConfig(WINDSPEED_ADC, WINDSPEED_ADC_CHANNEL, 1, ADC_SampleTime_144Cycles);
    
    // 使能ADC
    ADC_Cmd(WINDSPEED_ADC, ENABLE);
    
    // 等待ADC稳定
    delay_ms(10);
}

/**
  * @brief  读取ADC原始值
  * @param  None
  * @retval ADC原始值 (0-4095)
  */
uint16_t WindSpeed_ReadADC(void)
{
    // 启动ADC转换
    ADC_SoftwareStartConv(WINDSPEED_ADC);
    
    // 等待转换完成
    while(ADC_GetFlagStatus(WINDSPEED_ADC, ADC_FLAG_EOC) == RESET);
    
    // 读取转换结果
    return ADC_GetConversionValue(WINDSPEED_ADC);
}

/**
  * @brief  获取传感器输出电压
  * @param  None
  * @retval 电压值 (V)
  */
float WindSpeed_GetVoltage(void)
{
    uint16_t adc_value;
    float voltage;
    
    adc_value = WindSpeed_ReadADC();
    
    // 转换为实际电压值
    voltage = (float)adc_value * WINDSPEED_VREF / WINDSPEED_ADC_MAX_VALUE;
    
    // 考虑分压电路，还原实际传感器输出电压
    voltage = voltage * WINDSPEED_VOLTAGE_DIVIDER;
    
    return voltage;
}

/**
  * @brief  获取风速值
  * @param  None
  * @retval 风速值 (m/s)
  */
float WindSpeed_GetSpeed(void)
{
    float voltage;
    float wind_speed;
    
    voltage = WindSpeed_GetVoltage();
    
    // 根据传感器规格书进行线性转换
    // 0-5V对应0-60m/s，但根据表格，0-5V对应0-12m/s更准确
    // 使用线性插值：风速 = 电压 * 12 / 5
    wind_speed = voltage * 12.0f / 5.0f;
    
    // 限制范围
    if(wind_speed < 0.0f) wind_speed = 0.0f;
    if(wind_speed > 60.0f) wind_speed = 60.0f;
    
    return wind_speed;
}

/**
  * @brief  获取滤波后的风速值
  * @param  None
  * @retval 滤波后的风速值 (m/s)
  */
float WindSpeed_GetSpeedFiltered(void)
{
    float speed_sum = 0.0f;
    uint8_t i;
    
    // 多次采样求平均值，减少噪声
    for(i = 0; i < WINDSPEED_SAMPLE_COUNT; i++)
    {
        speed_sum += WindSpeed_GetSpeed();
        delay_ms(5);  // 小延时
    }
    
    return speed_sum / WINDSPEED_SAMPLE_COUNT;
}
