/**
  ******************************************************************************
  * @file    logo.c
  * <AUTHOR> Name
  * @brief   Michigear Logo Display Functions
  ******************************************************************************
  */

#include "logo.h"
#include "tftlcd.h"

// Simplified Michigear logo (40x40 pixels in RGB565 format)
// This creates a simple gear-like pattern in blue and yellow
const u16 michigear_logo[LOGO_WIDTH * LOGO_HEIGHT] = {
    // Row 0-4: Top border
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,
    0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,
    0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,
    0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,
    
    // Row 5-9: Gear teeth pattern
    0x001F,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,
    0x001F,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,
    0x001F,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,
    0x001F,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,
    0x001F,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,
    
    // Row 10-14: Outer ring
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    
    // Row 15-19: Inner yellow gear with M
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    
    // Row 20-24: M letter in center
    0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,
    0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,
    0x001F,0x001F,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,
    
    // Row 25-29: Continue M and gear
    0x001F,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,
    0x001F,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,
    0x001F,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,
    0x001F,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,
    0x001F,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,
    
    // Row 30-34: Lower gear section
    0x001F,0x001F,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,
    0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    
    // Row 35-39: Bottom border
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0xFFE0,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,
    0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F,0x001F
};

/**
  * @brief  Display Michigear Logo
  * @param  x: X coordinate
  * @param  y: Y coordinate
  * @retval None
  */
void Display_Logo(u16 x, u16 y)
{
    u16 i, j;
    u16 color;

    for(i = 0; i < LOGO_HEIGHT; i++)
    {
        for(j = 0; j < LOGO_WIDTH; j++)
        {
            color = michigear_logo[i * LOGO_WIDTH + j];
            LCD_DrawFRONT_COLOR(x + j, y + i, color);
        }
    }
}
