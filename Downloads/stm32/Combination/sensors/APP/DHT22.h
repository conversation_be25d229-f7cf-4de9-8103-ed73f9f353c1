/*
Library:					DHT22 - AM2302 Temperature and <PERSON><PERSON><PERSON><PERSON> Sensor
Written by:				<PERSON> (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mbedded YouTube Channel)
Date Written:			21/11/2018
Last modified:		Adapted for STM32F4xx Standard Library
Description:			This is an STM32 device driver library for the DHT22 Temperature and Humidity Sensor, using STM32F4xx Standard Library

References:				This library was written based on the DHT22 datasheet
											- https://cdn-shop.adafruit.com/datasheets/Digital+humidity+and+temperature+sensor+AM2302.pdf
											
* Copyright (C) 2018 - M. Yaqoob
   This is a free software under the GNU license, you can redistribute it and/or modify it under the terms
   of the GNU General Public Licenseversion 3 as published by the Free Software Foundation.
		
   This software library is shared with puplic for educational purposes, without WARRANTY and <PERSON> is not liable for any damages caused directly
   or indirectly by this software, read more about this on the GNU General Public License.
*/

#ifndef _DHT22_H
#define _DHT22_H

//Header files
#include "system.h"
#include "SysTick.h"
#include <stdbool.h>
#include <string.h>
#include <math.h>

//Pin Mode enum
typedef enum
{
	DHT22_OUTPUT = 0,
	DHT22_INPUT,
}DHT22_PinMode_Typedef;

//DHT22 GPIO配置
#define DHT22_GPIO_PORT		GPIOA
#define DHT22_GPIO_PIN		GPIO_Pin_9
#define DHT22_GPIO_CLK		RCC_AHB1Periph_GPIOA
#define DHT22_PIN_NUM		9

//*** Functions prototypes ***//
//DHT22初始化
void DHT22_Init(void);
//改变引脚模式
static void DHT22_PinMode(DHT22_PinMode_Typedef mode);
//DHT22引脚写入
static void DHT22_Pin_Write(bool state);
//DHT22引脚读取
static bool DHT22_Pin_Read(void);
//微秒延时
static void DHT22_DelayMicroSeconds(uint32_t uSec);
//开始采集
static void DHT22_StartAcquisition(void);
//读取5字节原始数据
static void DHT22_ReadRaw(uint8_t *data);

//获取温湿度数据
bool DHT22_GetTemp_Humidity(float *Temp, float *Humidity);

#endif
