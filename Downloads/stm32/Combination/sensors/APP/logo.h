/**
  ******************************************************************************
  * @file    logo.h
  * <AUTHOR> Name
  * @brief   Michigear Logo Data
  ******************************************************************************
  */

#ifndef __LOGO_H
#define __LOGO_H

#include "system.h"

// Logo dimensions
#define LOGO_WIDTH  40
#define LOGO_HEIGHT 40

// Function declarations
void Display_Logo(u16 x, u16 y);

// Simplified Michigear logo data (40x40 pixels)
// This is a simplified version - you'll need to convert your actual logo
extern const u16 michigear_logo[LOGO_WIDTH * LOGO_HEIGHT];

#endif /* __LOGO_H */
