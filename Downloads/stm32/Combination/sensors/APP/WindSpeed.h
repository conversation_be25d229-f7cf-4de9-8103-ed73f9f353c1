/**
  ******************************************************************************
  * @file    WindSpeed.h
  * <AUTHOR> Name
  * @brief   ZTS-3000-FSJT-V05 风速传感器驱动头文件
  ******************************************************************************
  */

#ifndef __WINDSPEED_H
#define __WINDSPEED_H

#include "system.h"
#include <stdbool.h>

// 风速传感器ADC配置
#define WINDSPEED_ADC_PORT          GPIOA
#define WINDSPEED_ADC_PIN           GPIO_Pin_0
#define WINDSPEED_ADC_CHANNEL       ADC_Channel_0
#define WINDSPEED_ADC_CLK           RCC_AHB1Periph_GPIOA

// ADC配置参数
#define WINDSPEED_ADC               ADC1
#define WINDSPEED_ADC_CLK_CMD       RCC_APB2Periph_ADC1

// 采样参数
#define WINDSPEED_SAMPLE_COUNT      10      // 采样次数，用于滤波
#define WINDSPEED_ADC_MAX_VALUE     4095    // 12位ADC最大值
#define WINDSPEED_VREF              3.3f    // 参考电压
#define WINDSPEED_VOLTAGE_DIVIDER   2.0f    // 分压系数 (两个10kΩ电阻，比例1:1)

// 风速计算参数（根据传感器规格书）
#define WINDSPEED_MAX_VOLTAGE       5.0f    // 传感器最大输出电压
#define WINDSPEED_MAX_SPEED         60.0f   // 对应的最大风速 m/s
#define WINDSPEED_MIN_VOLTAGE       0.0f    // 传感器最小输出电压
#define WINDSPEED_MIN_SPEED         0.0f    // 对应的最小风速 m/s

// 函数声明
void WindSpeed_Init(void);
uint16_t WindSpeed_ReadADC(void);
float WindSpeed_GetVoltage(void);
float WindSpeed_GetSpeed(void);
float WindSpeed_GetSpeedFiltered(void);

#endif /* __WINDSPEED_H */
