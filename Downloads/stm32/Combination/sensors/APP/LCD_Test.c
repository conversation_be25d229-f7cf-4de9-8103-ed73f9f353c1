/**
  ******************************************************************************
  * @file    LCD_Test.c
  * <AUTHOR> Name
  * @brief   WindSpeed Test with LCD Display
  ******************************************************************************
  */

#include "system.h"
#include "SysTick.h"
#include "tftlcd.h"
#include "WindSpeed.h"
#include <stdio.h>

/**
  * @brief  LCD WindSpeed Test
  * @param  None
  * @retval None
  */
void LCD_WindSpeed_Test(void)
{
    char str[50];
    int test_count = 0;
    
    // Clear screen and setup
    LCD_Clear(WHITE);
    FRONT_COLOR = BLACK;
    
    // Display title
    LCD_ShowString(10, 10, tftlcd_data.width, tftlcd_data.height, 20, "WindSpeed Test");
    LCD_ShowString(10, 35, tftlcd_data.width, tftlcd_data.height, 14, "Connection Check");
    
    // Initialize WindSpeed sensor
    WindSpeed_Init();
    LCD_ShowString(10, 60, tftlcd_data.width, tftlcd_data.height, 16, "Sensor Init: OK");
    
    // Display labels
    LCD_ShowString(10, 90, tftlcd_data.width, tftlcd_data.height, 16, "ADC Raw:");
    LCD_ShowString(10, 115, tftlcd_data.width, tftlcd_data.height, 16, "Voltage:");
    LCD_ShowString(10, 140, tftlcd_data.width, tftlcd_data.height, 16, "Wind Speed:");
    LCD_ShowString(10, 165, tftlcd_data.width, tftlcd_data.height, 16, "Status:");
    LCD_ShowString(10, 190, tftlcd_data.width, tftlcd_data.height, 16, "Test Count:");
    
    while(1)
    {
        uint16_t adc_raw;
        float voltage;
        float wind_speed;

        test_count++;

        // Read sensor data
        adc_raw = WindSpeed_ReadADC();
        voltage = WindSpeed_GetVoltage();
        wind_speed = WindSpeed_GetSpeed();
        
        // Clear previous data
        LCD_Fill(120, 90, 300, 220, WHITE);
        
        // Display ADC value
        sprintf(str, "%d", adc_raw);
        FRONT_COLOR = BLUE;
        LCD_ShowString(120, 90, tftlcd_data.width, tftlcd_data.height, 16, (u8*)str);
        
        // Display voltage
        sprintf(str, "%.2f V", voltage);
        LCD_ShowString(120, 115, tftlcd_data.width, tftlcd_data.height, 16, (u8*)str);
        
        // Display wind speed
        sprintf(str, "%.2f m/s", wind_speed);
        LCD_ShowString(120, 140, tftlcd_data.width, tftlcd_data.height, 16, (u8*)str);
        
        // Display status
        if(adc_raw == 0)
        {
            FRONT_COLOR = RED;
            LCD_ShowString(120, 165, tftlcd_data.width, tftlcd_data.height, 16, "ADC Error");
        }
        else if(adc_raw >= 4090)
        {
            FRONT_COLOR = RED;
            LCD_ShowString(120, 165, tftlcd_data.width, tftlcd_data.height, 16, "ADC Saturated");
        }
        else if(adc_raw > 2048)
        {
            FRONT_COLOR = YELLOW;
            LCD_ShowString(120, 165, tftlcd_data.width, tftlcd_data.height, 16, "ADC High");
        }
        else
        {
            FRONT_COLOR = GREEN;
            LCD_ShowString(120, 165, tftlcd_data.width, tftlcd_data.height, 16, "OK");
        }
        
        // Display test count
        sprintf(str, "%d", test_count);
        FRONT_COLOR = BLACK;
        LCD_ShowString(120, 190, tftlcd_data.width, tftlcd_data.height, 16, (u8*)str);
        
        // Expected ranges info
        FRONT_COLOR = GRAY;
        LCD_ShowString(10, 220, tftlcd_data.width, tftlcd_data.height, 12, "Expected:");
        LCD_ShowString(10, 235, tftlcd_data.width, tftlcd_data.height, 12, "ADC: 0-2048");
        LCD_ShowString(10, 250, tftlcd_data.width, tftlcd_data.height, 12, "Voltage: 0-5V");
        LCD_ShowString(10, 265, tftlcd_data.width, tftlcd_data.height, 12, "Blow air to test");
        
        delay_ms(1000);  // Update every 1 second
    }
}

/**
  * @brief  Simple LCD ADC Test
  * @param  None
  * @retval None
  */
void Simple_LCD_ADC_Test(void)
{
    char str[50];
    int count = 0;
    
    // Clear screen and setup
    LCD_Clear(WHITE);
    FRONT_COLOR = BLACK;
    
    // Display title
    LCD_ShowString(10, 10, tftlcd_data.width, tftlcd_data.height, 20, "ADC Test");
    LCD_ShowString(10, 35, tftlcd_data.width, tftlcd_data.height, 14, "Raw Values");
    
    // Initialize WindSpeed sensor
    WindSpeed_Init();
    LCD_ShowString(10, 60, tftlcd_data.width, tftlcd_data.height, 16, "Init: OK");
    
    // Display labels
    LCD_ShowString(10, 90, tftlcd_data.width, tftlcd_data.height, 16, "ADC:");
    LCD_ShowString(10, 115, tftlcd_data.width, tftlcd_data.height, 16, "MCU V:");
    LCD_ShowString(10, 140, tftlcd_data.width, tftlcd_data.height, 16, "Sensor V:");
    LCD_ShowString(10, 165, tftlcd_data.width, tftlcd_data.height, 16, "Count:");
    
    while(count < 100)  // Test 100 times
    {
        uint16_t adc;
        float voltage_mcu;
        float voltage_sensor;

        count++;

        // Read ADC
        adc = WindSpeed_ReadADC();
        voltage_mcu = (float)adc * 3.3f / 4095.0f;
        voltage_sensor = voltage_mcu * 2.0f;
        
        // Clear previous data
        LCD_Fill(100, 90, 250, 180, WHITE);
        
        // Display values
        FRONT_COLOR = BLUE;
        sprintf(str, "%d", adc);
        LCD_ShowString(100, 90, tftlcd_data.width, tftlcd_data.height, 16, (u8*)str);
        
        sprintf(str, "%.3f", voltage_mcu);
        LCD_ShowString(100, 115, tftlcd_data.width, tftlcd_data.height, 16, (u8*)str);
        
        sprintf(str, "%.3f", voltage_sensor);
        LCD_ShowString(100, 140, tftlcd_data.width, tftlcd_data.height, 16, (u8*)str);
        
        sprintf(str, "%d/100", count);
        FRONT_COLOR = BLACK;
        LCD_ShowString(100, 165, tftlcd_data.width, tftlcd_data.height, 16, (u8*)str);
        
        delay_ms(500);
    }
    
    // Test completed
    FRONT_COLOR = GREEN;
    LCD_ShowString(10, 200, tftlcd_data.width, tftlcd_data.height, 16, "Test Completed!");
    
    delay_ms(3000);
}
