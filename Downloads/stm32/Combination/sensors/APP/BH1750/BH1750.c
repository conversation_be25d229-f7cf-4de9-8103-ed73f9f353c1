#include "BH1750.h"
#include "SysTick.h"

/*
    应用说明：
    在访问I2C设备前，请先调用 i2c_CheckDevice() 检测I2C设备是否正常，该函数会配置GPIO
*/


static void I2C_BH1750_GPIOConfig(void);


/*
*********************************************************************************************************
*   函 数 名: i2c_Delay
*   功能说明: I2C总线位延迟，最快400KHz
*   形    参：无
*   返 回 值: 无
*********************************************************************************************************
*/
static void i2c_Delay(void)
{
    uint8_t i;

    /*
        下面的时间是通过逻辑分析仪测试得到的。
    工作条件：CPU主频168MHz ，MDK编译环境，1级优化

        STM32F4xx主频168MHz，需要调整延时循环次数
        循环次数为20时，适合168MHz主频的I2C时序
    */
    for(i = 0; i < 20; i++);
}

/*
*********************************************************************************************************
*   函 数 名: i2c_Start
*   功能说明: CPU发起I2C总线启动信号
*   形    参：无
*   返 回 值: 无
*********************************************************************************************************
*/
void i2c_Start(void)
{
    /* 当SCL高电平时，SDA出现一个下跳沿表示I2C总线启动信号 */
    BH1750_I2C_SDA_1();
    BH1750_I2C_SCL_1();
    i2c_Delay();
    BH1750_I2C_SDA_0();
    i2c_Delay();
    BH1750_I2C_SCL_0();
    i2c_Delay();
}

/*
*********************************************************************************************************
*   函 数 名: i2c_Start
*   功能说明: CPU发起I2C总线停止信号
*   形    参：无
*   返 回 值: 无
*********************************************************************************************************
*/
void i2c_Stop(void)
{
    /* 当SCL高电平时，SDA出现一个上跳沿表示I2C总线停止信号 */
    BH1750_I2C_SDA_0();
    BH1750_I2C_SCL_1();
    i2c_Delay();
    BH1750_I2C_SDA_1();
}

/*
*********************************************************************************************************
*   函 数 名: i2c_SendByte
*   功能说明: CPU向I2C总线设备发送8bit数据
*   形    参：_ucByte ： 等待发送的字节
*   返 回 值: 无
*********************************************************************************************************
*/
void i2c_SendByte(uint8_t _ucByte)
{
    uint8_t i;

    /* 先发送字节的高位bit7 */
    for(i = 0; i < 8; i++)
    {
        if(_ucByte & 0x80)
        {
            BH1750_I2C_SDA_1();
        }
        else
        {
            BH1750_I2C_SDA_0();
        }
        i2c_Delay();
        BH1750_I2C_SCL_1();
        i2c_Delay();
        BH1750_I2C_SCL_0();
        if(i == 7)
        {
            BH1750_I2C_SDA_1(); // 释放总线
        }
        _ucByte <<= 1;  /* 左移一个bit */
        i2c_Delay();
    }
}

/*
*********************************************************************************************************
*   函 数 名: i2c_ReadByte
*   功能说明: CPU从I2C总线设备读取8bit数据
*   形    参：无
*   返 回 值: 读到的数据
*********************************************************************************************************
*/
uint8_t i2c_ReadByte(void)
{
    uint8_t i;
    uint8_t value;

    /* 读到第1个bit为数据的bit7 */
    value = 0;
    for(i = 0; i < 8; i++)
    {
        value <<= 1;
        BH1750_I2C_SCL_1();
        i2c_Delay();
        if(BH1750_I2C_SDA_READ())
        {
            value++;
        }
        BH1750_I2C_SCL_0();
        i2c_Delay();
    }
    return value;
}

/*
*********************************************************************************************************
*   函 数 名: i2c_WaitAck
*   功能说明: CPU产生一个时钟，并读取器件的ACK应答信号
*   形    参：无
*   返 回 值: 返回0表示正确应答，1表示无器件响应
*********************************************************************************************************
*/
uint8_t i2c_WaitAck(void)
{
    uint8_t re;

    BH1750_I2C_SDA_1(); /* CPU释放SDA总线 */
    i2c_Delay();
    BH1750_I2C_SCL_1(); /* CPU驱动SCL = 1, 此时器件会返回ACK应答 */
    i2c_Delay();
    if(BH1750_I2C_SDA_READ())   /* CPU读取SDA口线状态 */
    {
        re = 1;
    }
    else
    {
        re = 0;
    }
    BH1750_I2C_SCL_0();
    i2c_Delay();
    return re;
}

/*
*********************************************************************************************************
*   函 数 名: i2c_Ack
*   功能说明: CPU产生一个ACK信号
*   形    参：无
*   返 回 值: 无
*********************************************************************************************************
*/
void i2c_Ack(void)
{
    BH1750_I2C_SDA_0(); /* CPU驱动SDA = 0 */
    i2c_Delay();
    BH1750_I2C_SCL_1(); /* CPU产生1个时钟 */
    i2c_Delay();
    BH1750_I2C_SCL_0();
    i2c_Delay();
    BH1750_I2C_SDA_1(); /* CPU释放SDA总线 */
}

/*
*********************************************************************************************************
*   函 数 名: i2c_NAck
*   功能说明: CPU产生1个NACK信号
*   形    参：无
*   返 回 值: 无
*********************************************************************************************************
*/
void i2c_NAck(void)
{
    BH1750_I2C_SDA_1(); /* CPU驱动SDA = 1 */
    i2c_Delay();
    BH1750_I2C_SCL_1(); /* CPU产生1个时钟 */
    i2c_Delay();
    BH1750_I2C_SCL_0();
    i2c_Delay();
}

/*
*********************************************************************************************************
*   函 数 名: I2C_BH1750_GPIOConfig
*   功能说明: 配置I2C总线的GPIO，采用模拟IO的方式实现
*   形    参：无
*   返 回 值: 无
*********************************************************************************************************
*/
static void I2C_BH1750_GPIOConfig(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    RCC_AHB1PeriphClockCmd(BH1750_RCC_I2C_PORT, ENABLE);    /* 打开GPIO时钟 */

    GPIO_InitStructure.GPIO_Pin = BH1750_I2C_SCL_PIN | BH1750_I2C_SDA_PIN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;    /* 输出模式 */
    GPIO_InitStructure.GPIO_OType = GPIO_OType_OD;   /* 开漏输出 */
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;     /* 上拉 */
    GPIO_Init(BH1750_GPIO_PORT_I2C, &GPIO_InitStructure);

    /* 给一个停止信号, 复位I2C总线上的所有设备到待机模式 */
    i2c_Stop();
}

/*
*********************************************************************************************************
*   函 数 名: i2c_CheckDevice
*   功能说明: 检测I2C总线设备，CPU向发送设备地址，然后读取设备应答来判断该设备是否存在
*   形    参：_Address：设备的I2C总线地址
*   返 回 值: 返回值 0 表示正确， 返回1表示未探测到
*********************************************************************************************************
*/
uint8_t i2c_CheckDevice(uint8_t _Address)
{
    uint8_t ucAck;
    i2c_Start();        /* 发送启动信号 */
    /* 发送设备地址+读写控制bit（0 = w， 1 = r) bit7 先传 */
    i2c_SendByte(_Address | BH1750_I2C_WR);
    ucAck = i2c_WaitAck();  /* 检测设备的ACK应答 */

    i2c_Stop();         /* 发送停止信号 */

    return ucAck;
}

//BH1750写一个字节
//返回值 成功：0      失败：非0
uint8_t BH1750_Byte_Write(uint8_t data)
{
    i2c_Start();
    //发送写地址
    i2c_SendByte(BH1750_Addr | 0);
    if(i2c_WaitAck() == 1)
    {
        return 1;
    }
    //发送控制命令
    i2c_SendByte(data);
    if(i2c_WaitAck() == 1)
    {
        return 2;
    }
    i2c_Stop();
    return 0;
}

//BH1750读取测量数据
//返回值 成功：返回光照强度     失败：返回0
uint16_t BH1750_Read_Measure(void)
{
    uint16_t receive_data = 0;
    i2c_Start();
    //发送读地址
    i2c_SendByte(BH1750_Addr | 1);
    if(i2c_WaitAck() == 1)
    {
        return 0;
    }
    //读取高八位
    receive_data = i2c_ReadByte();
    i2c_Ack();
    //读取低八位
    receive_data = (receive_data << 8) + i2c_ReadByte();
    i2c_NAck();
    i2c_Stop();
    return receive_data;    //返回读取到的数据
}


//BH1750s上电
void BH1750_Power_ON(void)
{
    BH1750_Byte_Write(POWER_ON);
}

//BH1750s断电
void BH1750_Power_OFF(void)
{
    BH1750_Byte_Write(POWER_OFF);
}

//BH1750复位  仅在上电时有效
void BH1750_RESET(void)
{
    BH1750_Byte_Write(MODULE_RESET);
}

//BH1750初始化
void BH1750_Init(void)
{
    I2C_BH1750_GPIOConfig();        /* 配置GPIO */

    BH1750_Power_ON();  //BH1750s上电
    //BH1750_RESET();           //BH1750复位
    BH1750_Byte_Write(Measure_Mode);
    delay_ms(120);  // 等待测量完成
}

//获取光照强度
float LIght_Intensity(void)
{
    return (float)(BH1750_Read_Measure() / 1.1f * Resolurtion);
}
