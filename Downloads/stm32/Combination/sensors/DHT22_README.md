# 环境监测系统 - DHT22温湿度 + BH1750光照传感器

## 项目说明
本项目基于STM32F407开发板，使用DHT22温湿度传感器和BH1750光照传感器采集环境数据，并在TFT LCD屏幕上实时显示温度、湿度和光照强度。

## 硬件连接
- **DHT22传感器连接**：
  - VCC → 3.3V 或 5V
  - GND → GND
  - DATA → PA9

- **BH1750光照传感器连接**：
  - VCC → 3.3V
  - GND → GND
  - SCL → PB12
  - SDA → PB13

- **LCD屏幕**：通过FSMC接口连接

- **LED指示灯**：用于指示系统运行状态

## 软件功能
1. **实时显示**：每2秒读取一次传感器数据并更新LCD显示
2. **数据格式**：
   - 温度：显示为 "XX.X C"
   - 湿度：显示为 "XX.X %"
   - 光照：显示为 "XX.X lx"
3. **状态指示**：
   - 全部正常：显示"OK"（绿色）
   - DHT22错误：显示"DHT22 Err"（红色），但光照数据仍正常显示
4. **调试信息**：LCD上显示详细的传感器读取状态
5. **统计功能**：显示总读取次数和错误次数
6. **串口输出**：同时通过USART1输出数据到串口调试助手
7. **LED闪烁**：LED1每0.5秒闪烁一次，指示系统正常运行

## 文件结构
```
show/
├── User/
│   └── main.c              # 主程序文件（已修改）
├── APP/
│   ├── DHT22.h            # DHT22传感器头文件（新增）
│   ├── DHT22.c            # DHT22传感器实现文件（新增）
│   ├── tftlcd/            # LCD显示相关文件
│   └── led/               # LED控制文件
├── Public/                # 系统公共文件
└── Libraries/             # STM32标准库文件
```

## 使用方法
1. **硬件连接**：按照上述连接方式连接DHT22传感器
2. **编译下载**：
   - 在Keil中打开Template.uvprojx项目文件
   - 编译项目
   - 下载到STM32F407开发板
3. **运行观察**：
   - LCD屏幕会显示温湿度数据
   - 串口调试助手可查看数据输出
   - LED1闪烁表示系统正常运行

## 显示界面
```
Environment Monitor
Temp & Humidity & Light

Temp:    25.6 C
Humi:    60.3 %
Light:   1250.5 lx
Status:  OK

Debug Info:
All OK: T=25.6 H=60.3 L=1250.5

Reads:   15
Errors:  0
```

## 注意事项
1. DHT22传感器需要稳定的电源供应
2. 数据线建议加上拉电阻（4.7kΩ-10kΩ）
3. DHT22响应时间较慢，建议读取间隔不少于2秒
4. 如果持续显示Error，请检查：
   - 硬件连接是否正确
   - 传感器是否损坏
   - 电源供应是否稳定

## 技术参数
- **MCU**：STM32F407ZGT6
- **系统时钟**：168MHz
- **传感器**：DHT22/AM2302
- **温度范围**：-40°C ~ +80°C（±0.5°C）
- **湿度范围**：0% ~ 100%RH（±2%RH）
- **通信接口**：单总线数字信号
- **显示屏**：TFT LCD（通过FSMC接口）

## 串口设置
- **波特率**：115200
- **数据位**：8
- **停止位**：1
- **校验位**：无
- **流控制**：无
