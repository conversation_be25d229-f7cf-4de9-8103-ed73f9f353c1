.\obj\windspeed.o: APP\WindSpeed.c
.\obj\windspeed.o: APP\WindSpeed.h
.\obj\windspeed.o: .\Public\system.h
.\obj\windspeed.o: .\User\stm32f4xx.h
.\obj\windspeed.o: .\Libraries\CMSIS\core_cm4.h
.\obj\windspeed.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\obj\windspeed.o: .\Libraries\CMSIS\core_cmInstr.h
.\obj\windspeed.o: .\Libraries\CMSIS\core_cmFunc.h
.\obj\windspeed.o: .\Libraries\CMSIS\core_cmSimd.h
.\obj\windspeed.o: .\Libraries\CMSIS\system_stm32f4xx.h
.\obj\windspeed.o: .\User\stm32f4xx_conf.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_adc.h
.\obj\windspeed.o: .\User\stm32f4xx.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_crc.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dbgmcu.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dma.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_exti.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_flash.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_gpio.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_i2c.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_iwdg.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_pwr.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rcc.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rtc.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_sdio.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_spi.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_syscfg.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_tim.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_usart.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_wwdg.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\misc.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_cryp.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_hash.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rng.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_can.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dac.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dcmi.h
.\obj\windspeed.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_fsmc.h
.\obj\windspeed.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\obj\windspeed.o: .\Public\SysTick.h
