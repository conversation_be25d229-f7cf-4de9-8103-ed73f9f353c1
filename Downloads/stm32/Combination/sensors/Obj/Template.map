Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(i.main) refers to systick.o(i.SysTick_Init) for SysTick_Init
    main.o(i.main) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to usart.o(i.USART1_Init) for USART1_Init
    main.o(i.main) refers to tftlcd.o(i.TFTLCD_Init) for TFTLCD_Init
    main.o(i.main) refers to dht22.o(i.DHT22_Init) for DHT22_Init
    main.o(i.main) refers to bh1750.o(i.BH1750_Init) for BH1750_Init
    main.o(i.main) refers to windspeed.o(i.WindSpeed_Init) for WindSpeed_Init
    main.o(i.main) refers to tftlcd.o(i.LCD_Clear) for LCD_Clear
    main.o(i.main) refers to tftlcd.o(i.LCD_ShowString) for LCD_ShowString
    main.o(i.main) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.main) refers to dht22.o(i.DHT22_GetTemp_Humidity) for DHT22_GetTemp_Humidity
    main.o(i.main) refers to bh1750.o(i.LIght_Intensity) for LIght_Intensity
    main.o(i.main) refers to windspeed.o(i.WindSpeed_GetSpeedFiltered) for WindSpeed_GetSpeedFiltered
    main.o(i.main) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.main) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(i.main) refers to tftlcd.o(i.LCD_Fill) for LCD_Fill
    main.o(i.main) refers to tftlcd.o(.data) for FRONT_COLOR
    main.o(i.main) refers to systick.o(i.delay_ms) for delay_ms
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_wwdg.o(i.WWDG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_rng.o(i.RNG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_pwr.o(i.PWR_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_adc.o(i.ADC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_dac.o(i.DAC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_GetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetTimeStamp) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_can.o(i.CAN_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to stm32f4xx_can.o(i.CheckITStatus) for CheckITStatus
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    systick.o(i.SysTick_Init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    systick.o(i.SysTick_Init) refers to systick.o(.data) for fac_us
    systick.o(i.delay_ms) refers to systick.o(i.delay_nms) for delay_nms
    systick.o(i.delay_nms) refers to systick.o(.data) for fac_ms
    systick.o(i.delay_us) refers to systick.o(.data) for fac_us
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for USART1_RX_STA
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for USART1_RX_BUF
    usart.o(i.USART1_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.USART1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.USART1_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart.o(i.USART1_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.USART1_Init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usart.o(i.USART1_Init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.USART1_Init) refers to stm32f4xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart.o(i.USART1_Init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.USART1_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.fputc) refers to stm32f4xx_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.fputc) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    led.o(i.LED_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    tftlcd.o(i.LCD_Clear) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_Clear) refers to tftlcd.o(i.LCD_WriteData_Color) for LCD_WriteData_Color
    tftlcd.o(i.LCD_Clear) refers to tftlcd.o(.data) for tftlcd_data
    tftlcd.o(i.LCD_Color_Fill) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_Color_Fill) refers to tftlcd.o(i.LCD_WriteData_Color) for LCD_WriteData_Color
    tftlcd.o(i.LCD_Display_Dir) refers to tftlcd.o(i.LCD_WriteCmd) for LCD_WriteCmd
    tftlcd.o(i.LCD_Display_Dir) refers to tftlcd.o(i.LCD_WriteData) for LCD_WriteData
    tftlcd.o(i.LCD_Display_Dir) refers to tftlcd.o(.data) for tftlcd_data
    tftlcd.o(i.LCD_DrawFRONT_COLOR) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_DrawFRONT_COLOR) refers to tftlcd.o(i.LCD_WriteData_Color) for LCD_WriteData_Color
    tftlcd.o(i.LCD_DrawLine) refers to tftlcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    tftlcd.o(i.LCD_DrawLine_Color) refers to tftlcd.o(i.LCD_DrawFRONT_COLOR) for LCD_DrawFRONT_COLOR
    tftlcd.o(i.LCD_DrawPoint) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_DrawPoint) refers to tftlcd.o(i.LCD_WriteData_Color) for LCD_WriteData_Color
    tftlcd.o(i.LCD_DrawPoint) refers to tftlcd.o(.data) for FRONT_COLOR
    tftlcd.o(i.LCD_DrawRectangle) refers to tftlcd.o(i.LCD_DrawLine) for LCD_DrawLine
    tftlcd.o(i.LCD_Draw_Circle) refers to tftlcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    tftlcd.o(i.LCD_DrowSign) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_DrowSign) refers to tftlcd.o(i.LCD_WriteData_Color) for LCD_WriteData_Color
    tftlcd.o(i.LCD_Fill) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_Fill) refers to tftlcd.o(i.LCD_WriteData_Color) for LCD_WriteData_Color
    tftlcd.o(i.LCD_ReadPoint) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_ReadPoint) refers to tftlcd.o(i.LCD_WriteCmd) for LCD_WriteCmd
    tftlcd.o(i.LCD_ReadPoint) refers to tftlcd.o(.data) for tftlcd_data
    tftlcd.o(i.LCD_SSD_BackLightSet) refers to tftlcd.o(i.LCD_WriteCmd) for LCD_WriteCmd
    tftlcd.o(i.LCD_SSD_BackLightSet) refers to tftlcd.o(i.LCD_WriteData) for LCD_WriteData
    tftlcd.o(i.LCD_SSD_BackLightSet) refers to dfltui.o(.text) for __aeabi_ui2d
    tftlcd.o(i.LCD_SSD_BackLightSet) refers to dmul.o(.text) for __aeabi_dmul
    tftlcd.o(i.LCD_SSD_BackLightSet) refers to dfixui.o(.text) for __aeabi_d2uiz
    tftlcd.o(i.LCD_Set_Window) refers to tftlcd.o(i.LCD_WriteCmd) for LCD_WriteCmd
    tftlcd.o(i.LCD_Set_Window) refers to tftlcd.o(i.LCD_WriteData) for LCD_WriteData
    tftlcd.o(i.LCD_ShowChar) refers to tftlcd.o(i.LCD_DrawFRONT_COLOR) for LCD_DrawFRONT_COLOR
    tftlcd.o(i.LCD_ShowChar) refers to tftlcd.o(.constdata) for ascii_1206
    tftlcd.o(i.LCD_ShowChar) refers to tftlcd.o(.data) for FRONT_COLOR
    tftlcd.o(i.LCD_ShowFontHZ) refers to tftlcd.o(i.LCD_DrawFRONT_COLOR) for LCD_DrawFRONT_COLOR
    tftlcd.o(i.LCD_ShowFontHZ) refers to tftlcd.o(.constdata) for CnChar32x29
    tftlcd.o(i.LCD_ShowFontHZ) refers to tftlcd.o(.data) for FRONT_COLOR
    tftlcd.o(i.LCD_ShowNum) refers to tftlcd.o(i.LCD_Pow) for LCD_Pow
    tftlcd.o(i.LCD_ShowNum) refers to tftlcd.o(i.LCD_ShowChar) for LCD_ShowChar
    tftlcd.o(i.LCD_ShowPicture) refers to tftlcd.o(i.LCD_Set_Window) for LCD_Set_Window
    tftlcd.o(i.LCD_ShowPicture) refers to tftlcd.o(i.LCD_WriteData_Color) for LCD_WriteData_Color
    tftlcd.o(i.LCD_ShowString) refers to tftlcd.o(i.LCD_ShowChar) for LCD_ShowChar
    tftlcd.o(i.LCD_ShowxNum) refers to tftlcd.o(i.LCD_Pow) for LCD_Pow
    tftlcd.o(i.LCD_ShowxNum) refers to tftlcd.o(i.LCD_ShowChar) for LCD_ShowChar
    tftlcd.o(i.LCD_WriteCmdData) refers to tftlcd.o(i.LCD_WriteCmd) for LCD_WriteCmd
    tftlcd.o(i.LCD_WriteCmdData) refers to tftlcd.o(i.LCD_WriteData) for LCD_WriteData
    tftlcd.o(i.TFTLCD_FSMC_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd) for RCC_AHB3PeriphClockCmd
    tftlcd.o(i.TFTLCD_FSMC_Init) refers to stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit) for FSMC_NORSRAMInit
    tftlcd.o(i.TFTLCD_FSMC_Init) refers to stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd) for FSMC_NORSRAMCmd
    tftlcd.o(i.TFTLCD_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    tftlcd.o(i.TFTLCD_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    tftlcd.o(i.TFTLCD_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.TFTLCD_GPIO_Init) for TFTLCD_GPIO_Init
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.TFTLCD_FSMC_Init) for TFTLCD_FSMC_Init
    tftlcd.o(i.TFTLCD_Init) refers to systick.o(i.delay_ms) for delay_ms
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.LCD_WriteCmd) for LCD_WriteCmd
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.LCD_ReadData) for LCD_ReadData
    tftlcd.o(i.TFTLCD_Init) refers to printfa.o(i.__0printf) for __2printf
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.LCD_WriteData) for LCD_WriteData
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.LCD_Display_Dir) for LCD_Display_Dir
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(i.LCD_Clear) for LCD_Clear
    tftlcd.o(i.TFTLCD_Init) refers to tftlcd.o(.data) for tftlcd_data
    dht22.o(i.DHT22_GetTemp_Humidity) refers to dht22.o(i.DHT22_StartAcquisition) for DHT22_StartAcquisition
    dht22.o(i.DHT22_GetTemp_Humidity) refers to dht22.o(i.DHT22_ReadRaw) for DHT22_ReadRaw
    dht22.o(i.DHT22_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    dht22.o(i.DHT22_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    dht22.o(i.DHT22_Init) refers to dht22.o(i.DHT22_Pin_Write) for DHT22_Pin_Write
    dht22.o(i.DHT22_PinMode) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    dht22.o(i.DHT22_ReadRaw) refers to dht22.o(i.DHT22_DelayMicroSeconds) for DHT22_DelayMicroSeconds
    dht22.o(i.DHT22_ReadRaw) refers to dht22.o(i.DHT22_Pin_Read) for DHT22_Pin_Read
    dht22.o(i.DHT22_StartAcquisition) refers to dht22.o(i.DHT22_PinMode) for DHT22_PinMode
    dht22.o(i.DHT22_StartAcquisition) refers to dht22.o(i.DHT22_Pin_Write) for DHT22_Pin_Write
    dht22.o(i.DHT22_StartAcquisition) refers to dht22.o(i.DHT22_DelayMicroSeconds) for DHT22_DelayMicroSeconds
    lcd_test.o(i.LCD_WindSpeed_Test) refers to tftlcd.o(i.LCD_Clear) for LCD_Clear
    lcd_test.o(i.LCD_WindSpeed_Test) refers to tftlcd.o(i.LCD_ShowString) for LCD_ShowString
    lcd_test.o(i.LCD_WindSpeed_Test) refers to windspeed.o(i.WindSpeed_Init) for WindSpeed_Init
    lcd_test.o(i.LCD_WindSpeed_Test) refers to windspeed.o(i.WindSpeed_ReadADC) for WindSpeed_ReadADC
    lcd_test.o(i.LCD_WindSpeed_Test) refers to windspeed.o(i.WindSpeed_GetVoltage) for WindSpeed_GetVoltage
    lcd_test.o(i.LCD_WindSpeed_Test) refers to windspeed.o(i.WindSpeed_GetSpeed) for WindSpeed_GetSpeed
    lcd_test.o(i.LCD_WindSpeed_Test) refers to tftlcd.o(i.LCD_Fill) for LCD_Fill
    lcd_test.o(i.LCD_WindSpeed_Test) refers to printfa.o(i.__0sprintf) for __2sprintf
    lcd_test.o(i.LCD_WindSpeed_Test) refers to f2d.o(.text) for __aeabi_f2d
    lcd_test.o(i.LCD_WindSpeed_Test) refers to systick.o(i.delay_ms) for delay_ms
    lcd_test.o(i.LCD_WindSpeed_Test) refers to tftlcd.o(.data) for FRONT_COLOR
    lcd_test.o(i.Simple_LCD_ADC_Test) refers to tftlcd.o(i.LCD_Clear) for LCD_Clear
    lcd_test.o(i.Simple_LCD_ADC_Test) refers to tftlcd.o(i.LCD_ShowString) for LCD_ShowString
    lcd_test.o(i.Simple_LCD_ADC_Test) refers to windspeed.o(i.WindSpeed_Init) for WindSpeed_Init
    lcd_test.o(i.Simple_LCD_ADC_Test) refers to windspeed.o(i.WindSpeed_ReadADC) for WindSpeed_ReadADC
    lcd_test.o(i.Simple_LCD_ADC_Test) refers to tftlcd.o(i.LCD_Fill) for LCD_Fill
    lcd_test.o(i.Simple_LCD_ADC_Test) refers to printfa.o(i.__0sprintf) for __2sprintf
    lcd_test.o(i.Simple_LCD_ADC_Test) refers to f2d.o(.text) for __aeabi_f2d
    lcd_test.o(i.Simple_LCD_ADC_Test) refers to systick.o(i.delay_ms) for delay_ms
    lcd_test.o(i.Simple_LCD_ADC_Test) refers to tftlcd.o(.data) for FRONT_COLOR
    windspeed.o(i.WindSpeed_GetSpeed) refers to windspeed.o(i.WindSpeed_GetVoltage) for WindSpeed_GetVoltage
    windspeed.o(i.WindSpeed_GetSpeedFiltered) refers to windspeed.o(i.WindSpeed_GetSpeed) for WindSpeed_GetSpeed
    windspeed.o(i.WindSpeed_GetSpeedFiltered) refers to systick.o(i.delay_ms) for delay_ms
    windspeed.o(i.WindSpeed_GetVoltage) refers to windspeed.o(i.WindSpeed_ReadADC) for WindSpeed_ReadADC
    windspeed.o(i.WindSpeed_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    windspeed.o(i.WindSpeed_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    windspeed.o(i.WindSpeed_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    windspeed.o(i.WindSpeed_Init) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    windspeed.o(i.WindSpeed_Init) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    windspeed.o(i.WindSpeed_Init) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    windspeed.o(i.WindSpeed_Init) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    windspeed.o(i.WindSpeed_Init) refers to systick.o(i.delay_ms) for delay_ms
    windspeed.o(i.WindSpeed_ReadADC) refers to stm32f4xx_adc.o(i.ADC_SoftwareStartConv) for ADC_SoftwareStartConv
    windspeed.o(i.WindSpeed_ReadADC) refers to stm32f4xx_adc.o(i.ADC_GetFlagStatus) for ADC_GetFlagStatus
    windspeed.o(i.WindSpeed_ReadADC) refers to stm32f4xx_adc.o(i.ADC_GetConversionValue) for ADC_GetConversionValue
    bh1750.o(i.BH1750_Byte_Write) refers to bh1750.o(i.i2c_Start) for i2c_Start
    bh1750.o(i.BH1750_Byte_Write) refers to bh1750.o(i.i2c_SendByte) for i2c_SendByte
    bh1750.o(i.BH1750_Byte_Write) refers to bh1750.o(i.i2c_WaitAck) for i2c_WaitAck
    bh1750.o(i.BH1750_Byte_Write) refers to bh1750.o(i.i2c_Stop) for i2c_Stop
    bh1750.o(i.BH1750_Init) refers to bh1750.o(i.I2C_BH1750_GPIOConfig) for I2C_BH1750_GPIOConfig
    bh1750.o(i.BH1750_Init) refers to bh1750.o(i.BH1750_Power_ON) for BH1750_Power_ON
    bh1750.o(i.BH1750_Init) refers to bh1750.o(i.BH1750_Byte_Write) for BH1750_Byte_Write
    bh1750.o(i.BH1750_Init) refers to systick.o(i.delay_ms) for delay_ms
    bh1750.o(i.BH1750_Power_OFF) refers to bh1750.o(i.BH1750_Byte_Write) for BH1750_Byte_Write
    bh1750.o(i.BH1750_Power_ON) refers to bh1750.o(i.BH1750_Byte_Write) for BH1750_Byte_Write
    bh1750.o(i.BH1750_RESET) refers to bh1750.o(i.BH1750_Byte_Write) for BH1750_Byte_Write
    bh1750.o(i.BH1750_Read_Measure) refers to bh1750.o(i.i2c_Start) for i2c_Start
    bh1750.o(i.BH1750_Read_Measure) refers to bh1750.o(i.i2c_SendByte) for i2c_SendByte
    bh1750.o(i.BH1750_Read_Measure) refers to bh1750.o(i.i2c_WaitAck) for i2c_WaitAck
    bh1750.o(i.BH1750_Read_Measure) refers to bh1750.o(i.i2c_ReadByte) for i2c_ReadByte
    bh1750.o(i.BH1750_Read_Measure) refers to bh1750.o(i.i2c_Ack) for i2c_Ack
    bh1750.o(i.BH1750_Read_Measure) refers to bh1750.o(i.i2c_NAck) for i2c_NAck
    bh1750.o(i.BH1750_Read_Measure) refers to bh1750.o(i.i2c_Stop) for i2c_Stop
    bh1750.o(i.I2C_BH1750_GPIOConfig) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bh1750.o(i.I2C_BH1750_GPIOConfig) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    bh1750.o(i.I2C_BH1750_GPIOConfig) refers to bh1750.o(i.i2c_Stop) for i2c_Stop
    bh1750.o(i.LIght_Intensity) refers to bh1750.o(i.BH1750_Read_Measure) for BH1750_Read_Measure
    bh1750.o(i.i2c_Ack) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    bh1750.o(i.i2c_Ack) refers to bh1750.o(i.i2c_Delay) for i2c_Delay
    bh1750.o(i.i2c_Ack) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    bh1750.o(i.i2c_CheckDevice) refers to bh1750.o(i.i2c_Start) for i2c_Start
    bh1750.o(i.i2c_CheckDevice) refers to bh1750.o(i.i2c_SendByte) for i2c_SendByte
    bh1750.o(i.i2c_CheckDevice) refers to bh1750.o(i.i2c_WaitAck) for i2c_WaitAck
    bh1750.o(i.i2c_CheckDevice) refers to bh1750.o(i.i2c_Stop) for i2c_Stop
    bh1750.o(i.i2c_NAck) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    bh1750.o(i.i2c_NAck) refers to bh1750.o(i.i2c_Delay) for i2c_Delay
    bh1750.o(i.i2c_NAck) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    bh1750.o(i.i2c_ReadByte) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    bh1750.o(i.i2c_ReadByte) refers to bh1750.o(i.i2c_Delay) for i2c_Delay
    bh1750.o(i.i2c_ReadByte) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    bh1750.o(i.i2c_ReadByte) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    bh1750.o(i.i2c_SendByte) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    bh1750.o(i.i2c_SendByte) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    bh1750.o(i.i2c_SendByte) refers to bh1750.o(i.i2c_Delay) for i2c_Delay
    bh1750.o(i.i2c_Start) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    bh1750.o(i.i2c_Start) refers to bh1750.o(i.i2c_Delay) for i2c_Delay
    bh1750.o(i.i2c_Start) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    bh1750.o(i.i2c_Stop) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    bh1750.o(i.i2c_Stop) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    bh1750.o(i.i2c_Stop) refers to bh1750.o(i.i2c_Delay) for i2c_Delay
    bh1750.o(i.i2c_WaitAck) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    bh1750.o(i.i2c_WaitAck) refers to bh1750.o(i.i2c_Delay) for i2c_Delay
    bh1750.o(i.i2c_WaitAck) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    bh1750.o(i.i2c_WaitAck) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    logo.o(i.Display_Logo) refers to tftlcd.o(i.LCD_DrawFRONT_COLOR) for LCD_DrawFRONT_COLOR
    logo.o(i.Display_Logo) refers to logo.o(.constdata) for michigear_logo
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.constdata), (44800 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing startup_stm32f40_41xxx.o(HEAP), (512 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (312 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (34 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_DeInit), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetFlagStatus), (64 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (44 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (60 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (22 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig), (64 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (12 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(.rrx_text), (6 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetITStatus), (24 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_tim.o(i.TI1_Config), (58 bytes).
    Removing stm32f4xx_tim.o(i.TI2_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f4xx_tim.o(i.TI4_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_Cmd), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DeInit), (400 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICInit), (110 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1Init), (124 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4Init), (120 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_RemapConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOCxM), (86 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare4), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseInit), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (32 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (240 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearFlag), (16 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearITPendingBit), (16 bytes).
    Removing stm32f4xx_rng.o(i.RNG_Cmd), (36 bytes).
    Removing stm32f4xx_rng.o(i.RNG_DeInit), (20 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetITStatus), (24 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetRandomNumber), (12 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ITConfig), (36 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupRegulatorCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterUnderDriveSTOPMode), (68 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_FlashPowerDownCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_MainRegulatorModeConfig), (24 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveSWCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_UnderDriveCmd), (36 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonStructInit), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ContinuousModeCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DeInit), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetITStatus), (38 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetMultiModeConversionValue), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ITConfig), (32 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd), (44 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_StructInit), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd), (44 bytes).
    Removing stm32f4xx_adc.o(i.ADC_VBATCmd), (44 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearFlag), (16 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearITPendingBit), (16 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetFlagStatus), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetITStatus), (48 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ITConfig), (40 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f4xx_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearFlag), (52 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearITPendingBit), (52 bytes).
    Removing stm32f4xx_dma.o(i.DMA_Cmd), (22 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DeInit), (344 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd), (22 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_FlowControllerConfig), (22 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCmdStatus), (20 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget), (20 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFIFOStatus), (12 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFlagStatus), (68 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetITStatus), (100 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ITConfig), (58 bytes).
    Removing stm32f4xx_dma.o(i.DMA_Init), (88 bytes).
    Removing stm32f4xx_dma.o(i.DMA_MemoryTargetConfig), (10 bytes).
    Removing stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig), (22 bytes).
    Removing stm32f4xx_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f4xx_dma.o(i.DMA_StructInit), (34 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmCmd), (116 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmStructInit), (22 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmSubSecondConfig), (52 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Bcd2ToByte), (22 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_BypassShadowCmd), (60 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ByteToBcd2), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputCmd), (60 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputConfig), (48 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearFlag), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearITPendingBit), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig), (56 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DateStructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DayLightSavingConfig), (56 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DeInit), (212 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_EnterInitMode), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ExitInitMode), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarm), (116 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarmSubSecond), (36 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetDate), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetFlagStatus), (40 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetITStatus), (64 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetStoreOperation), (16 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetSubSecond), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTime), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStamp), (156 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStampSubSecond), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetWakeUpCounter), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ITConfig), (100 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Init), (100 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputConfig), (56 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputTypeConfig), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ReadBackupRegister), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_RefClockCmd), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetAlarm), (236 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetDate), (200 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetTime), (208 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetWakeUpCounter), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SmoothCalibConfig), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_StructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperCmd), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperFilterConfig), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinSelection), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinsPrechargeDuration), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPullUpCmd), (36 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperSamplingFreqConfig), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperTriggerConfig), (36 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampCmd), (56 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampOnTamperDetectionCmd), (36 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampPinSelection), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStructInit), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WaitForSynchro), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpClockConfig), (48 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpCmd), (120 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteBackupRegister), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteProtectionCmd), (28 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f4xx_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f4xx_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f4xx_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f4xx_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f4xx_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f4xx_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f4xx_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f4xx_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f4xx_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f4xx_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f4xx_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f4xx_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f4xx_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f4xx_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f4xx_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f4xx_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f4xx_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDInit), (212 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit), (52 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDInit), (196 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing system_stm32f4xx.o(.data), (20 bytes).
    Removing system.o(.rev16_text), (4 bytes).
    Removing system.o(.revsh_text), (4 bytes).
    Removing system.o(.rrx_text), (6 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing systick.o(.rrx_text), (6 bytes).
    Removing systick.o(i.delay_us), (76 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing tftlcd.o(.rev16_text), (4 bytes).
    Removing tftlcd.o(.revsh_text), (4 bytes).
    Removing tftlcd.o(.rrx_text), (6 bytes).
    Removing tftlcd.o(i.LCD_Color_Fill), (94 bytes).
    Removing tftlcd.o(i.LCD_DrawLine), (176 bytes).
    Removing tftlcd.o(i.LCD_DrawLine_Color), (172 bytes).
    Removing tftlcd.o(i.LCD_DrawPoint), (32 bytes).
    Removing tftlcd.o(i.LCD_DrawRectangle), (60 bytes).
    Removing tftlcd.o(i.LCD_Draw_Circle), (152 bytes).
    Removing tftlcd.o(i.LCD_DrowSign), (132 bytes).
    Removing tftlcd.o(i.LCD_Pow), (22 bytes).
    Removing tftlcd.o(i.LCD_RGBColor_Change), (28 bytes).
    Removing tftlcd.o(i.LCD_ReadPoint), (100 bytes).
    Removing tftlcd.o(i.LCD_SSD_BackLightSet), (96 bytes).
    Removing tftlcd.o(i.LCD_ShowFontHZ), (232 bytes).
    Removing tftlcd.o(i.LCD_ShowNum), (148 bytes).
    Removing tftlcd.o(i.LCD_ShowPicture), (92 bytes).
    Removing tftlcd.o(i.LCD_ShowxNum), (190 bytes).
    Removing tftlcd.o(i.LCD_WriteCmdData), (20 bytes).
    Removing dht22.o(.rev16_text), (4 bytes).
    Removing dht22.o(.revsh_text), (4 bytes).
    Removing dht22.o(.rrx_text), (6 bytes).
    Removing lcd_test.o(.rev16_text), (4 bytes).
    Removing lcd_test.o(.revsh_text), (4 bytes).
    Removing lcd_test.o(.rrx_text), (6 bytes).
    Removing lcd_test.o(i.LCD_WindSpeed_Test), (908 bytes).
    Removing lcd_test.o(i.Simple_LCD_ADC_Test), (600 bytes).
    Removing windspeed.o(.rev16_text), (4 bytes).
    Removing windspeed.o(.revsh_text), (4 bytes).
    Removing windspeed.o(.rrx_text), (6 bytes).
    Removing bh1750.o(.rev16_text), (4 bytes).
    Removing bh1750.o(.revsh_text), (4 bytes).
    Removing bh1750.o(.rrx_text), (6 bytes).
    Removing bh1750.o(i.BH1750_Power_OFF), (10 bytes).
    Removing bh1750.o(i.BH1750_RESET), (10 bytes).
    Removing bh1750.o(i.i2c_CheckDevice), (28 bytes).
    Removing logo.o(.rev16_text), (4 bytes).
    Removing logo.o(.revsh_text), (4 bytes).
    Removing logo.o(.rrx_text), (6 bytes).
    Removing logo.o(i.Display_Logo), (68 bytes).
    Removing logo.o(.constdata), (3200 bytes).
    Removing dfltui.o(.text), (26 bytes).
    Removing dfixui.o(.text), (50 bytes).

513 unused section(s) (total 69646 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    APP\BH1750\BH1750.c                      0x00000000   Number         0  bh1750.o ABSOLUTE
    APP\DHT22.c                              0x00000000   Number         0  dht22.o ABSOLUTE
    APP\LCD_Test.c                           0x00000000   Number         0  lcd_test.o ABSOLUTE
    APP\WindSpeed.c                          0x00000000   Number         0  windspeed.o ABSOLUTE
    APP\\BH1750\\BH1750.c                    0x00000000   Number         0  bh1750.o ABSOLUTE
    APP\\DHT22.c                             0x00000000   Number         0  dht22.o ABSOLUTE
    APP\\LCD_Test.c                          0x00000000   Number         0  lcd_test.o ABSOLUTE
    APP\\WindSpeed.c                         0x00000000   Number         0  windspeed.o ABSOLUTE
    APP\\led\\led.c                          0x00000000   Number         0  led.o ABSOLUTE
    APP\\logo.c                              0x00000000   Number         0  logo.o ABSOLUTE
    APP\\tftlcd\\tftlcd.c                    0x00000000   Number         0  tftlcd.o ABSOLUTE
    APP\led\led.c                            0x00000000   Number         0  led.o ABSOLUTE
    APP\logo.c                               0x00000000   Number         0  logo.o ABSOLUTE
    APP\tftlcd\tftlcd.c                      0x00000000   Number         0  tftlcd.o ABSOLUTE
    Libraries\CMSIS\startup_stm32f40_41xxx.s 0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    Libraries\CMSIS\system_stm32f4xx.c       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_can.c 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dac.c 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_fsmc.c 0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_iwdg.c 0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_pwr.c 0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rng.c 0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rtc.c 0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_syscfg.c 0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_wwdg.c 0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    Libraries\\CMSIS\\system_stm32f4xx.c     0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_can.c 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dac.c 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_fsmc.c 0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_iwdg.c 0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_pwr.c 0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rng.c 0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rtc.c 0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_syscfg.c 0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_wwdg.c 0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    Public\SysTick.c                         0x00000000   Number         0  systick.o ABSOLUTE
    Public\\SysTick.c                        0x00000000   Number         0  systick.o ABSOLUTE
    Public\\system.c                         0x00000000   Number         0  system.o ABSOLUTE
    Public\\usart.c                          0x00000000   Number         0  usart.o ABSOLUTE
    Public\system.c                          0x00000000   Number         0  system.o ABSOLUTE
    Public\usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    User\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    User\\stm32f4xx_it.c                     0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f4xx_it.c                      0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000198   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000198   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000198   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000198   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x0800019c   Section       36  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x0800019c   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x080001c0   Section        0  dmul.o(.text)
    .text                                    0x080002a4   Section        0  f2d.o(.text)
    .text                                    0x080002ca   Section        0  uidiv.o(.text)
    .text                                    0x080002f6   Section        0  uldiv.o(.text)
    .text                                    0x08000358   Section        0  llushr.o(.text)
    .text                                    0x08000378   Section        0  depilogue.o(.text)
    .text                                    0x08000378   Section        0  iusefp.o(.text)
    .text                                    0x08000432   Section        0  dadd.o(.text)
    .text                                    0x08000580   Section        0  ddiv.o(.text)
    .text                                    0x0800065e   Section        0  dfixul.o(.text)
    .text                                    0x08000690   Section       48  cdrcmple.o(.text)
    .text                                    0x080006c0   Section       36  init.o(.text)
    .text                                    0x080006e4   Section        0  llshl.o(.text)
    .text                                    0x08000702   Section        0  llsshr.o(.text)
    i.ADC_Cmd                                0x08000726   Section        0  stm32f4xx_adc.o(i.ADC_Cmd)
    i.ADC_CommonInit                         0x0800073c   Section        0  stm32f4xx_adc.o(i.ADC_CommonInit)
    i.ADC_GetConversionValue                 0x0800076c   Section        0  stm32f4xx_adc.o(i.ADC_GetConversionValue)
    i.ADC_GetFlagStatus                      0x08000774   Section        0  stm32f4xx_adc.o(i.ADC_GetFlagStatus)
    i.ADC_Init                               0x08000788   Section        0  stm32f4xx_adc.o(i.ADC_Init)
    i.ADC_RegularChannelConfig               0x080007dc   Section        0  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    i.ADC_SoftwareStartConv                  0x08000894   Section        0  stm32f4xx_adc.o(i.ADC_SoftwareStartConv)
    i.BH1750_Byte_Write                      0x0800089e   Section        0  bh1750.o(i.BH1750_Byte_Write)
    i.BH1750_Init                            0x080008d0   Section        0  bh1750.o(i.BH1750_Init)
    i.BH1750_Power_ON                        0x080008e8   Section        0  bh1750.o(i.BH1750_Power_ON)
    i.BH1750_Read_Measure                    0x080008f2   Section        0  bh1750.o(i.BH1750_Read_Measure)
    i.BusFault_Handler                       0x0800092c   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DHT22_DelayMicroSeconds                0x08000930   Section        0  dht22.o(i.DHT22_DelayMicroSeconds)
    DHT22_DelayMicroSeconds                  0x08000931   Thumb Code    24  dht22.o(i.DHT22_DelayMicroSeconds)
    i.DHT22_GetTemp_Humidity                 0x08000948   Section        0  dht22.o(i.DHT22_GetTemp_Humidity)
    i.DHT22_Init                             0x080009c8   Section        0  dht22.o(i.DHT22_Init)
    i.DHT22_PinMode                          0x08000a04   Section        0  dht22.o(i.DHT22_PinMode)
    DHT22_PinMode                            0x08000a05   Thumb Code    64  dht22.o(i.DHT22_PinMode)
    i.DHT22_Pin_Read                         0x08000a48   Section        0  dht22.o(i.DHT22_Pin_Read)
    DHT22_Pin_Read                           0x08000a49   Thumb Code    14  dht22.o(i.DHT22_Pin_Read)
    i.DHT22_Pin_Write                        0x08000a5c   Section        0  dht22.o(i.DHT22_Pin_Write)
    DHT22_Pin_Write                          0x08000a5d   Thumb Code    18  dht22.o(i.DHT22_Pin_Write)
    i.DHT22_ReadRaw                          0x08000a74   Section        0  dht22.o(i.DHT22_ReadRaw)
    DHT22_ReadRaw                            0x08000a75   Thumb Code   158  dht22.o(i.DHT22_ReadRaw)
    i.DHT22_StartAcquisition                 0x08000b12   Section        0  dht22.o(i.DHT22_StartAcquisition)
    DHT22_StartAcquisition                   0x08000b13   Thumb Code    42  dht22.o(i.DHT22_StartAcquisition)
    i.DebugMon_Handler                       0x08000b3c   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.FSMC_NORSRAMCmd                        0x08000b40   Section        0  stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd)
    i.FSMC_NORSRAMInit                       0x08000b74   Section        0  stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit)
    i.GPIO_Init                              0x08000c9c   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x08000d2c   Section        0  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    i.GPIO_ReadInputDataBit                  0x08000d72   Section        0  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08000d84   Section        0  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000d88   Section        0  stm32f4xx_gpio.o(i.GPIO_SetBits)
    i.HardFault_Handler                      0x08000d8c   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_BH1750_GPIOConfig                  0x08000d90   Section        0  bh1750.o(i.I2C_BH1750_GPIOConfig)
    I2C_BH1750_GPIOConfig                    0x08000d91   Thumb Code    50  bh1750.o(i.I2C_BH1750_GPIOConfig)
    i.LCD_Clear                              0x08000dc8   Section        0  tftlcd.o(i.LCD_Clear)
    i.LCD_Display_Dir                        0x08000e10   Section        0  tftlcd.o(i.LCD_Display_Dir)
    i.LCD_DrawFRONT_COLOR                    0x08000e58   Section        0  tftlcd.o(i.LCD_DrawFRONT_COLOR)
    i.LCD_Fill                               0x08000e74   Section        0  tftlcd.o(i.LCD_Fill)
    i.LCD_ReadData                           0x08000ed0   Section        0  tftlcd.o(i.LCD_ReadData)
    i.LCD_Set_Window                         0x08000edc   Section        0  tftlcd.o(i.LCD_Set_Window)
    i.LCD_ShowChar                           0x08000f28   Section        0  tftlcd.o(i.LCD_ShowChar)
    i.LCD_ShowString                         0x08001050   Section        0  tftlcd.o(i.LCD_ShowString)
    i.LCD_WriteCmd                           0x080010b8   Section        0  tftlcd.o(i.LCD_WriteCmd)
    i.LCD_WriteData                          0x080010c4   Section        0  tftlcd.o(i.LCD_WriteData)
    i.LCD_WriteData_Color                    0x080010d0   Section        0  tftlcd.o(i.LCD_WriteData_Color)
    i.LED_Init                               0x080010e8   Section        0  led.o(i.LED_Init)
    i.LIght_Intensity                        0x08001140   Section        0  bh1750.o(i.LIght_Intensity)
    i.MemManage_Handler                      0x08001164   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001168   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x0800116c   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x080011e4   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PendSV_Handler                         0x080011f8   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.RCC_AHB1PeriphClockCmd                 0x080011fc   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_AHB3PeriphClockCmd                 0x0800121c   Section        0  stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x0800123c   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x0800125c   Section        0  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08001344   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08001348   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x08001349   Thumb Code   220  system_stm32f4xx.o(i.SetSysClock)
    i.SysTick_CLKSourceConfig                0x08001434   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x0800145c   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SysTick_Init                           0x08001460   Section        0  systick.o(i.SysTick_Init)
    i.SystemInit                             0x0800149c   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TFTLCD_FSMC_Init                       0x08001504   Section        0  tftlcd.o(i.TFTLCD_FSMC_Init)
    i.TFTLCD_GPIO_Init                       0x08001584   Section        0  tftlcd.o(i.TFTLCD_GPIO_Init)
    i.TFTLCD_Init                            0x0800173c   Section        0  tftlcd.o(i.TFTLCD_Init)
    i.USART1_IRQHandler                      0x080018fc   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART1_Init                            0x08001984   Section        0  usart.o(i.USART1_Init)
    i.USART_ClearFlag                        0x08001a38   Section        0  stm32f4xx_usart.o(i.USART_ClearFlag)
    i.USART_Cmd                              0x08001a4a   Section        0  stm32f4xx_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08001a62   Section        0  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08001a7c   Section        0  stm32f4xx_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08001ad0   Section        0  stm32f4xx_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08001b1c   Section        0  stm32f4xx_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08001bf0   Section        0  stm32f4xx_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x08001bfa   Section        0  stm32f4xx_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x08001c02   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.WindSpeed_GetSpeed                     0x08001c08   Section        0  windspeed.o(i.WindSpeed_GetSpeed)
    i.WindSpeed_GetSpeedFiltered             0x08001c64   Section        0  windspeed.o(i.WindSpeed_GetSpeedFiltered)
    i.WindSpeed_GetVoltage                   0x08001ca4   Section        0  windspeed.o(i.WindSpeed_GetVoltage)
    i.WindSpeed_Init                         0x08001ce8   Section        0  windspeed.o(i.WindSpeed_Init)
    i.WindSpeed_ReadADC                      0x08001d70   Section        0  windspeed.o(i.WindSpeed_ReadADC)
    i.__0printf                              0x08001d94   Section        0  printfa.o(i.__0printf)
    i.__0sprintf                             0x08001db4   Section        0  printfa.o(i.__0sprintf)
    i.__scatterload_copy                     0x08001ddc   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08001dea   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08001dec   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x08001dfc   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08001dfd   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08001f80   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08001f81   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x0800265c   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x0800265d   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08002680   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08002681   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x080026ae   Section        0  printfa.o(i._sputc)
    _sputc                                   0x080026af   Thumb Code    10  printfa.o(i._sputc)
    i.delay_ms                               0x080026b8   Section        0  systick.o(i.delay_ms)
    i.delay_nms                              0x080026f0   Section        0  systick.o(i.delay_nms)
    i.fputc                                  0x0800273c   Section        0  usart.o(i.fputc)
    i.i2c_Ack                                0x08002760   Section        0  bh1750.o(i.i2c_Ack)
    i.i2c_Delay                              0x0800279c   Section        0  bh1750.o(i.i2c_Delay)
    i2c_Delay                                0x0800279d   Thumb Code    14  bh1750.o(i.i2c_Delay)
    i.i2c_NAck                               0x080027ac   Section        0  bh1750.o(i.i2c_NAck)
    i.i2c_ReadByte                           0x080027e0   Section        0  bh1750.o(i.i2c_ReadByte)
    i.i2c_SendByte                           0x08002828   Section        0  bh1750.o(i.i2c_SendByte)
    i.i2c_Start                              0x0800288c   Section        0  bh1750.o(i.i2c_Start)
    i.i2c_Stop                               0x080028c8   Section        0  bh1750.o(i.i2c_Stop)
    i.i2c_WaitAck                            0x080028f4   Section        0  bh1750.o(i.i2c_WaitAck)
    i.main                                   0x0800293c   Section        0  main.o(i.main)
    .constdata                               0x08003220   Section    18712  tftlcd.o(.constdata)
    .data                                    0x20000000   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x20000010   Section        4  systick.o(.data)
    fac_us                                   0x20000010   Data           1  systick.o(.data)
    fac_ms                                   0x20000012   Data           2  systick.o(.data)
    .data                                    0x20000014   Section        2  usart.o(.data)
    .data                                    0x20000016   Section       12  tftlcd.o(.data)
    .data                                    0x20000024   Section        4  stdout.o(.data)
    .bss                                     0x20000028   Section      200  usart.o(.bss)
    STACK                                    0x200000f0   Section     1024  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000199   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000199   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x0800019d   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __aeabi_dmul                             0x080001c1   Thumb Code   228  dmul.o(.text)
    __aeabi_f2d                              0x080002a5   Thumb Code    38  f2d.o(.text)
    __aeabi_uidiv                            0x080002cb   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080002cb   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x080002f7   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsr                             0x08000359   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000359   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x08000379   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x08000379   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000397   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x08000433   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000575   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x0800057b   Thumb Code     6  dadd.o(.text)
    __aeabi_ddiv                             0x08000581   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x0800065f   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000691   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x080006c1   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080006c1   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x080006e5   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080006e5   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x08000703   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000703   Thumb Code     0  llsshr.o(.text)
    ADC_Cmd                                  0x08000727   Thumb Code    22  stm32f4xx_adc.o(i.ADC_Cmd)
    ADC_CommonInit                           0x0800073d   Thumb Code    34  stm32f4xx_adc.o(i.ADC_CommonInit)
    ADC_GetConversionValue                   0x0800076d   Thumb Code     8  stm32f4xx_adc.o(i.ADC_GetConversionValue)
    ADC_GetFlagStatus                        0x08000775   Thumb Code    18  stm32f4xx_adc.o(i.ADC_GetFlagStatus)
    ADC_Init                                 0x08000789   Thumb Code    74  stm32f4xx_adc.o(i.ADC_Init)
    ADC_RegularChannelConfig                 0x080007dd   Thumb Code   184  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    ADC_SoftwareStartConv                    0x08000895   Thumb Code    10  stm32f4xx_adc.o(i.ADC_SoftwareStartConv)
    BH1750_Byte_Write                        0x0800089f   Thumb Code    50  bh1750.o(i.BH1750_Byte_Write)
    BH1750_Init                              0x080008d1   Thumb Code    24  bh1750.o(i.BH1750_Init)
    BH1750_Power_ON                          0x080008e9   Thumb Code    10  bh1750.o(i.BH1750_Power_ON)
    BH1750_Read_Measure                      0x080008f3   Thumb Code    58  bh1750.o(i.BH1750_Read_Measure)
    BusFault_Handler                         0x0800092d   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DHT22_GetTemp_Humidity                   0x08000949   Thumb Code   126  dht22.o(i.DHT22_GetTemp_Humidity)
    DHT22_Init                               0x080009c9   Thumb Code    56  dht22.o(i.DHT22_Init)
    DebugMon_Handler                         0x08000b3d   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    FSMC_NORSRAMCmd                          0x08000b41   Thumb Code    46  stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd)
    FSMC_NORSRAMInit                         0x08000b75   Thumb Code   290  stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit)
    GPIO_Init                                0x08000c9d   Thumb Code   144  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x08000d2d   Thumb Code    70  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    GPIO_ReadInputDataBit                    0x08000d73   Thumb Code    18  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08000d85   Thumb Code     4  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000d89   Thumb Code     4  stm32f4xx_gpio.o(i.GPIO_SetBits)
    HardFault_Handler                        0x08000d8d   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    LCD_Clear                                0x08000dc9   Thumb Code    68  tftlcd.o(i.LCD_Clear)
    LCD_Display_Dir                          0x08000e11   Thumb Code    66  tftlcd.o(i.LCD_Display_Dir)
    LCD_DrawFRONT_COLOR                      0x08000e59   Thumb Code    28  tftlcd.o(i.LCD_DrawFRONT_COLOR)
    LCD_Fill                                 0x08000e75   Thumb Code    92  tftlcd.o(i.LCD_Fill)
    LCD_ReadData                             0x08000ed1   Thumb Code     6  tftlcd.o(i.LCD_ReadData)
    LCD_Set_Window                           0x08000edd   Thumb Code    74  tftlcd.o(i.LCD_Set_Window)
    LCD_ShowChar                             0x08000f29   Thumb Code   272  tftlcd.o(i.LCD_ShowChar)
    LCD_ShowString                           0x08001051   Thumb Code   102  tftlcd.o(i.LCD_ShowString)
    LCD_WriteCmd                             0x080010b9   Thumb Code     6  tftlcd.o(i.LCD_WriteCmd)
    LCD_WriteData                            0x080010c5   Thumb Code     6  tftlcd.o(i.LCD_WriteData)
    LCD_WriteData_Color                      0x080010d1   Thumb Code    18  tftlcd.o(i.LCD_WriteData_Color)
    LED_Init                                 0x080010e9   Thumb Code    82  led.o(i.LED_Init)
    LIght_Intensity                          0x08001141   Thumb Code    32  bh1750.o(i.LIght_Intensity)
    MemManage_Handler                        0x08001165   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001169   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x0800116d   Thumb Code   106  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x080011e5   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PendSV_Handler                           0x080011f9   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    RCC_AHB1PeriphClockCmd                   0x080011fd   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_AHB3PeriphClockCmd                   0x0800121d   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x0800123d   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x0800125d   Thumb Code   214  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08001345   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_CLKSourceConfig                  0x08001435   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x0800145d   Thumb Code     2  stm32f4xx_it.o(i.SysTick_Handler)
    SysTick_Init                             0x08001461   Thumb Code    52  systick.o(i.SysTick_Init)
    SystemInit                               0x0800149d   Thumb Code    88  system_stm32f4xx.o(i.SystemInit)
    TFTLCD_FSMC_Init                         0x08001505   Thumb Code   126  tftlcd.o(i.TFTLCD_FSMC_Init)
    TFTLCD_GPIO_Init                         0x08001585   Thumb Code   416  tftlcd.o(i.TFTLCD_GPIO_Init)
    TFTLCD_Init                              0x0800173d   Thumb Code   426  tftlcd.o(i.TFTLCD_Init)
    USART1_IRQHandler                        0x080018fd   Thumb Code   122  usart.o(i.USART1_IRQHandler)
    USART1_Init                              0x08001985   Thumb Code   172  usart.o(i.USART1_Init)
    USART_ClearFlag                          0x08001a39   Thumb Code    18  stm32f4xx_usart.o(i.USART_ClearFlag)
    USART_Cmd                                0x08001a4b   Thumb Code    24  stm32f4xx_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08001a63   Thumb Code    26  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08001a7d   Thumb Code    84  stm32f4xx_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08001ad1   Thumb Code    74  stm32f4xx_usart.o(i.USART_ITConfig)
    USART_Init                               0x08001b1d   Thumb Code   204  stm32f4xx_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08001bf1   Thumb Code    10  stm32f4xx_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x08001bfb   Thumb Code     8  stm32f4xx_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x08001c03   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    WindSpeed_GetSpeed                       0x08001c09   Thumb Code    84  windspeed.o(i.WindSpeed_GetSpeed)
    WindSpeed_GetSpeedFiltered               0x08001c65   Thumb Code    58  windspeed.o(i.WindSpeed_GetSpeedFiltered)
    WindSpeed_GetVoltage                     0x08001ca5   Thumb Code    60  windspeed.o(i.WindSpeed_GetVoltage)
    WindSpeed_Init                           0x08001ce9   Thumb Code   128  windspeed.o(i.WindSpeed_Init)
    WindSpeed_ReadADC                        0x08001d71   Thumb Code    30  windspeed.o(i.WindSpeed_ReadADC)
    __0printf                                0x08001d95   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08001d95   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08001d95   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08001d95   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08001d95   Thumb Code     0  printfa.o(i.__0printf)
    __0sprintf                               0x08001db5   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x08001db5   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08001db5   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08001db5   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08001db5   Thumb Code     0  printfa.o(i.__0sprintf)
    __scatterload_copy                       0x08001ddd   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08001deb   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08001ded   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    delay_ms                                 0x080026b9   Thumb Code    56  systick.o(i.delay_ms)
    delay_nms                                0x080026f1   Thumb Code    72  systick.o(i.delay_nms)
    fputc                                    0x0800273d   Thumb Code    32  usart.o(i.fputc)
    i2c_Ack                                  0x08002761   Thumb Code    56  bh1750.o(i.i2c_Ack)
    i2c_NAck                                 0x080027ad   Thumb Code    46  bh1750.o(i.i2c_NAck)
    i2c_ReadByte                             0x080027e1   Thumb Code    68  bh1750.o(i.i2c_ReadByte)
    i2c_SendByte                             0x08002829   Thumb Code    96  bh1750.o(i.i2c_SendByte)
    i2c_Start                                0x0800288d   Thumb Code    56  bh1750.o(i.i2c_Start)
    i2c_Stop                                 0x080028c9   Thumb Code    38  bh1750.o(i.i2c_Stop)
    i2c_WaitAck                              0x080028f5   Thumb Code    66  bh1750.o(i.i2c_WaitAck)
    main                                     0x0800293d   Thumb Code  2050  main.o(i.main)
    ascii_1206                               0x08003220   Data        1140  tftlcd.o(.constdata)
    ascii_1608                               0x08003694   Data        1520  tftlcd.o(.constdata)
    ascii_2412                               0x08003c84   Data        3420  tftlcd.o(.constdata)
    ascii_3216                               0x080049e0   Data       12160  tftlcd.o(.constdata)
    CnChar32x29                              0x08007960   Data         472  tftlcd.o(.constdata)
    Region$$Table$$Base                      0x08007b38   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08007b58   Number         0  anon$$obj.o(Region$$Table)
    USART1_RX_STA                            0x20000014   Data           2  usart.o(.data)
    FRONT_COLOR                              0x20000016   Data           2  tftlcd.o(.data)
    BACK_COLOR                               0x20000018   Data           2  tftlcd.o(.data)
    tftlcd_data                              0x2000001a   Data           8  tftlcd.o(.data)
    __stdout                                 0x20000024   Data           4  stdout.o(.data)
    USART1_RX_BUF                            0x20000028   Data         200  usart.o(.bss)
    __initial_sp                             0x200004f0   Data           0  startup_stm32f40_41xxx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007b80, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00007b58, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          279    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000000   Code   RO         3810  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         4082    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         4085    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         4087    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         4089    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         4090    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         4092    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         4094    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         4083    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x0800019c   0x0800019c   0x00000024   Code   RO          280    .text               startup_stm32f40_41xxx.o
    0x080001c0   0x080001c0   0x000000e4   Code   RO         4074    .text               mf_w.l(dmul.o)
    0x080002a4   0x080002a4   0x00000026   Code   RO         4080    .text               mf_w.l(f2d.o)
    0x080002ca   0x080002ca   0x0000002c   Code   RO         4097    .text               mc_w.l(uidiv.o)
    0x080002f6   0x080002f6   0x00000062   Code   RO         4099    .text               mc_w.l(uldiv.o)
    0x08000358   0x08000358   0x00000020   Code   RO         4101    .text               mc_w.l(llushr.o)
    0x08000378   0x08000378   0x00000000   Code   RO         4103    .text               mc_w.l(iusefp.o)
    0x08000378   0x08000378   0x000000ba   Code   RO         4104    .text               mf_w.l(depilogue.o)
    0x08000432   0x08000432   0x0000014e   Code   RO         4106    .text               mf_w.l(dadd.o)
    0x08000580   0x08000580   0x000000de   Code   RO         4108    .text               mf_w.l(ddiv.o)
    0x0800065e   0x0800065e   0x00000030   Code   RO         4110    .text               mf_w.l(dfixul.o)
    0x0800068e   0x0800068e   0x00000002   PAD
    0x08000690   0x08000690   0x00000030   Code   RO         4112    .text               mf_w.l(cdrcmple.o)
    0x080006c0   0x080006c0   0x00000024   Code   RO         4114    .text               mc_w.l(init.o)
    0x080006e4   0x080006e4   0x0000001e   Code   RO         4116    .text               mc_w.l(llshl.o)
    0x08000702   0x08000702   0x00000024   Code   RO         4118    .text               mc_w.l(llsshr.o)
    0x08000726   0x08000726   0x00000016   Code   RO         1965    i.ADC_Cmd           stm32f4xx_adc.o
    0x0800073c   0x0800073c   0x00000030   Code   RO         1966    i.ADC_CommonInit    stm32f4xx_adc.o
    0x0800076c   0x0800076c   0x00000008   Code   RO         1977    i.ADC_GetConversionValue  stm32f4xx_adc.o
    0x08000774   0x08000774   0x00000012   Code   RO         1978    i.ADC_GetFlagStatus  stm32f4xx_adc.o
    0x08000786   0x08000786   0x00000002   PAD
    0x08000788   0x08000788   0x00000054   Code   RO         1985    i.ADC_Init          stm32f4xx_adc.o
    0x080007dc   0x080007dc   0x000000b8   Code   RO         1990    i.ADC_RegularChannelConfig  stm32f4xx_adc.o
    0x08000894   0x08000894   0x0000000a   Code   RO         1992    i.ADC_SoftwareStartConv  stm32f4xx_adc.o
    0x0800089e   0x0800089e   0x00000032   Code   RO         3662    i.BH1750_Byte_Write  bh1750.o
    0x080008d0   0x080008d0   0x00000018   Code   RO         3663    i.BH1750_Init       bh1750.o
    0x080008e8   0x080008e8   0x0000000a   Code   RO         3665    i.BH1750_Power_ON   bh1750.o
    0x080008f2   0x080008f2   0x0000003a   Code   RO         3667    i.BH1750_Read_Measure  bh1750.o
    0x0800092c   0x0800092c   0x00000004   Code   RO          183    i.BusFault_Handler  stm32f4xx_it.o
    0x08000930   0x08000930   0x00000018   Code   RO         3511    i.DHT22_DelayMicroSeconds  dht22.o
    0x08000948   0x08000948   0x0000007e   Code   RO         3512    i.DHT22_GetTemp_Humidity  dht22.o
    0x080009c6   0x080009c6   0x00000002   PAD
    0x080009c8   0x080009c8   0x0000003c   Code   RO         3513    i.DHT22_Init        dht22.o
    0x08000a04   0x08000a04   0x00000044   Code   RO         3514    i.DHT22_PinMode     dht22.o
    0x08000a48   0x08000a48   0x00000014   Code   RO         3515    i.DHT22_Pin_Read    dht22.o
    0x08000a5c   0x08000a5c   0x00000018   Code   RO         3516    i.DHT22_Pin_Write   dht22.o
    0x08000a74   0x08000a74   0x0000009e   Code   RO         3517    i.DHT22_ReadRaw     dht22.o
    0x08000b12   0x08000b12   0x0000002a   Code   RO         3518    i.DHT22_StartAcquisition  dht22.o
    0x08000b3c   0x08000b3c   0x00000002   Code   RO          184    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000b3e   0x08000b3e   0x00000002   PAD
    0x08000b40   0x08000b40   0x00000034   Code   RO         2993    i.FSMC_NORSRAMCmd   stm32f4xx_fsmc.o
    0x08000b74   0x08000b74   0x00000128   Code   RO         2995    i.FSMC_NORSRAMInit  stm32f4xx_fsmc.o
    0x08000c9c   0x08000c9c   0x00000090   Code   RO          288    i.GPIO_Init         stm32f4xx_gpio.o
    0x08000d2c   0x08000d2c   0x00000046   Code   RO          289    i.GPIO_PinAFConfig  stm32f4xx_gpio.o
    0x08000d72   0x08000d72   0x00000012   Code   RO          292    i.GPIO_ReadInputDataBit  stm32f4xx_gpio.o
    0x08000d84   0x08000d84   0x00000004   Code   RO          295    i.GPIO_ResetBits    stm32f4xx_gpio.o
    0x08000d88   0x08000d88   0x00000004   Code   RO          296    i.GPIO_SetBits      stm32f4xx_gpio.o
    0x08000d8c   0x08000d8c   0x00000004   Code   RO          185    i.HardFault_Handler  stm32f4xx_it.o
    0x08000d90   0x08000d90   0x00000038   Code   RO         3668    i.I2C_BH1750_GPIOConfig  bh1750.o
    0x08000dc8   0x08000dc8   0x00000048   Code   RO         3296    i.LCD_Clear         tftlcd.o
    0x08000e10   0x08000e10   0x00000048   Code   RO         3298    i.LCD_Display_Dir   tftlcd.o
    0x08000e58   0x08000e58   0x0000001c   Code   RO         3299    i.LCD_DrawFRONT_COLOR  tftlcd.o
    0x08000e74   0x08000e74   0x0000005c   Code   RO         3306    i.LCD_Fill          tftlcd.o
    0x08000ed0   0x08000ed0   0x0000000c   Code   RO         3309    i.LCD_ReadData      tftlcd.o
    0x08000edc   0x08000edc   0x0000004a   Code   RO         3312    i.LCD_Set_Window    tftlcd.o
    0x08000f26   0x08000f26   0x00000002   PAD
    0x08000f28   0x08000f28   0x00000128   Code   RO         3313    i.LCD_ShowChar      tftlcd.o
    0x08001050   0x08001050   0x00000066   Code   RO         3317    i.LCD_ShowString    tftlcd.o
    0x080010b6   0x080010b6   0x00000002   PAD
    0x080010b8   0x080010b8   0x0000000c   Code   RO         3319    i.LCD_WriteCmd      tftlcd.o
    0x080010c4   0x080010c4   0x0000000c   Code   RO         3321    i.LCD_WriteData     tftlcd.o
    0x080010d0   0x080010d0   0x00000018   Code   RO         3322    i.LCD_WriteData_Color  tftlcd.o
    0x080010e8   0x080010e8   0x00000058   Code   RO         3269    i.LED_Init          led.o
    0x08001140   0x08001140   0x00000024   Code   RO         3669    i.LIght_Intensity   bh1750.o
    0x08001164   0x08001164   0x00000004   Code   RO          186    i.MemManage_Handler  stm32f4xx_it.o
    0x08001168   0x08001168   0x00000002   Code   RO          187    i.NMI_Handler       stm32f4xx_it.o
    0x0800116a   0x0800116a   0x00000002   PAD
    0x0800116c   0x0800116c   0x00000078   Code   RO          787    i.NVIC_Init         misc.o
    0x080011e4   0x080011e4   0x00000014   Code   RO          788    i.NVIC_PriorityGroupConfig  misc.o
    0x080011f8   0x080011f8   0x00000002   Code   RO          188    i.PendSV_Handler    stm32f4xx_it.o
    0x080011fa   0x080011fa   0x00000002   PAD
    0x080011fc   0x080011fc   0x00000020   Code   RO          389    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x0800121c   0x0800121c   0x00000020   Code   RO          395    i.RCC_AHB3PeriphClockCmd  stm32f4xx_rcc.o
    0x0800123c   0x0800123c   0x00000020   Code   RO          401    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x0800125c   0x0800125c   0x000000e8   Code   RO          410    i.RCC_GetClocksFreq  stm32f4xx_rcc.o
    0x08001344   0x08001344   0x00000002   Code   RO          189    i.SVC_Handler       stm32f4xx_it.o
    0x08001346   0x08001346   0x00000002   PAD
    0x08001348   0x08001348   0x000000ec   Code   RO         3117    i.SetSysClock       system_stm32f4xx.o
    0x08001434   0x08001434   0x00000028   Code   RO          791    i.SysTick_CLKSourceConfig  misc.o
    0x0800145c   0x0800145c   0x00000002   Code   RO          190    i.SysTick_Handler   stm32f4xx_it.o
    0x0800145e   0x0800145e   0x00000002   PAD
    0x08001460   0x08001460   0x0000003c   Code   RO         3179    i.SysTick_Init      systick.o
    0x0800149c   0x0800149c   0x00000068   Code   RO         3119    i.SystemInit        system_stm32f4xx.o
    0x08001504   0x08001504   0x0000007e   Code   RO         3323    i.TFTLCD_FSMC_Init  tftlcd.o
    0x08001582   0x08001582   0x00000002   PAD
    0x08001584   0x08001584   0x000001b8   Code   RO         3324    i.TFTLCD_GPIO_Init  tftlcd.o
    0x0800173c   0x0800173c   0x000001c0   Code   RO         3325    i.TFTLCD_Init       tftlcd.o
    0x080018fc   0x080018fc   0x00000088   Code   RO         3226    i.USART1_IRQHandler  usart.o
    0x08001984   0x08001984   0x000000b4   Code   RO         3227    i.USART1_Init       usart.o
    0x08001a38   0x08001a38   0x00000012   Code   RO         1467    i.USART_ClearFlag   stm32f4xx_usart.o
    0x08001a4a   0x08001a4a   0x00000018   Code   RO         1471    i.USART_Cmd         stm32f4xx_usart.o
    0x08001a62   0x08001a62   0x0000001a   Code   RO         1474    i.USART_GetFlagStatus  stm32f4xx_usart.o
    0x08001a7c   0x08001a7c   0x00000054   Code   RO         1475    i.USART_GetITStatus  stm32f4xx_usart.o
    0x08001ad0   0x08001ad0   0x0000004a   Code   RO         1477    i.USART_ITConfig    stm32f4xx_usart.o
    0x08001b1a   0x08001b1a   0x00000002   PAD
    0x08001b1c   0x08001b1c   0x000000d4   Code   RO         1478    i.USART_Init        stm32f4xx_usart.o
    0x08001bf0   0x08001bf0   0x0000000a   Code   RO         1485    i.USART_ReceiveData  stm32f4xx_usart.o
    0x08001bfa   0x08001bfa   0x00000008   Code   RO         1488    i.USART_SendData    stm32f4xx_usart.o
    0x08001c02   0x08001c02   0x00000004   Code   RO          191    i.UsageFault_Handler  stm32f4xx_it.o
    0x08001c06   0x08001c06   0x00000002   PAD
    0x08001c08   0x08001c08   0x0000005c   Code   RO         3614    i.WindSpeed_GetSpeed  windspeed.o
    0x08001c64   0x08001c64   0x00000040   Code   RO         3615    i.WindSpeed_GetSpeedFiltered  windspeed.o
    0x08001ca4   0x08001ca4   0x00000044   Code   RO         3616    i.WindSpeed_GetVoltage  windspeed.o
    0x08001ce8   0x08001ce8   0x00000088   Code   RO         3617    i.WindSpeed_Init    windspeed.o
    0x08001d70   0x08001d70   0x00000024   Code   RO         3618    i.WindSpeed_ReadADC  windspeed.o
    0x08001d94   0x08001d94   0x00000020   Code   RO         4046    i.__0printf         mc_w.l(printfa.o)
    0x08001db4   0x08001db4   0x00000028   Code   RO         4048    i.__0sprintf        mc_w.l(printfa.o)
    0x08001ddc   0x08001ddc   0x0000000e   Code   RO         4122    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08001dea   0x08001dea   0x00000002   Code   RO         4123    i.__scatterload_null  mc_w.l(handlers.o)
    0x08001dec   0x08001dec   0x0000000e   Code   RO         4124    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08001dfa   0x08001dfa   0x00000002   PAD
    0x08001dfc   0x08001dfc   0x00000184   Code   RO         4053    i._fp_digits        mc_w.l(printfa.o)
    0x08001f80   0x08001f80   0x000006dc   Code   RO         4054    i._printf_core      mc_w.l(printfa.o)
    0x0800265c   0x0800265c   0x00000024   Code   RO         4055    i._printf_post_padding  mc_w.l(printfa.o)
    0x08002680   0x08002680   0x0000002e   Code   RO         4056    i._printf_pre_padding  mc_w.l(printfa.o)
    0x080026ae   0x080026ae   0x0000000a   Code   RO         4058    i._sputc            mc_w.l(printfa.o)
    0x080026b8   0x080026b8   0x00000038   Code   RO         3180    i.delay_ms          systick.o
    0x080026f0   0x080026f0   0x0000004c   Code   RO         3181    i.delay_nms         systick.o
    0x0800273c   0x0800273c   0x00000024   Code   RO         3228    i.fputc             usart.o
    0x08002760   0x08002760   0x0000003c   Code   RO         3670    i.i2c_Ack           bh1750.o
    0x0800279c   0x0800279c   0x0000000e   Code   RO         3672    i.i2c_Delay         bh1750.o
    0x080027aa   0x080027aa   0x00000002   PAD
    0x080027ac   0x080027ac   0x00000034   Code   RO         3673    i.i2c_NAck          bh1750.o
    0x080027e0   0x080027e0   0x00000048   Code   RO         3674    i.i2c_ReadByte      bh1750.o
    0x08002828   0x08002828   0x00000064   Code   RO         3675    i.i2c_SendByte      bh1750.o
    0x0800288c   0x0800288c   0x0000003c   Code   RO         3676    i.i2c_Start         bh1750.o
    0x080028c8   0x080028c8   0x0000002c   Code   RO         3677    i.i2c_Stop          bh1750.o
    0x080028f4   0x080028f4   0x00000048   Code   RO         3678    i.i2c_WaitAck       bh1750.o
    0x0800293c   0x0800293c   0x000008e4   Code   RO            4    i.main              main.o
    0x08003220   0x08003220   0x00004918   Data   RO         3326    .constdata          tftlcd.o
    0x08007b38   0x08007b38   0x00000020   Data   RO         4120    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08007b58, Size: 0x000004f0, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08007b58   0x00000010   Data   RW          442    .data               stm32f4xx_rcc.o
    0x20000010   0x08007b68   0x00000004   Data   RW         3183    .data               systick.o
    0x20000014   0x08007b6c   0x00000002   Data   RW         3230    .data               usart.o
    0x20000016   0x08007b6e   0x0000000c   Data   RW         3327    .data               tftlcd.o
    0x20000022   0x08007b7a   0x00000002   PAD
    0x20000024   0x08007b7c   0x00000004   Data   RW         4096    .data               mc_w.l(stdout.o)
    0x20000028        -       0x000000c8   Zero   RW         3229    .bss                usart.o
    0x200000f0        -       0x00000400   Zero   RW          277    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       708         44          0          0          0       7236   bh1750.o
       522         20          0          0          0       5422   dht22.o
        88          6          0          0          0        515   led.o
      2276        462          0          0          0     292743   main.o
       180         24          0          0          0       2257   misc.o
        36          8        392          0       1024        836   startup_stm32f40_41xxx.o
       374         24          0          0          0       5890   stm32f4xx_adc.o
       348         12          0          0          0       2145   stm32f4xx_fsmc.o
       240          0          0          0          0       3779   stm32f4xx_gpio.o
        26          0          0          0          0       3990   stm32f4xx_it.o
       328         36          0         16          0       4851   stm32f4xx_rcc.o
       456          8          0          0          0       6427   stm32f4xx_usart.o
       340         32          0          0          0       1533   system_stm32f4xx.o
       192         12          0          4          0       1850   systick.o
      1810        104      18712         12          0      11453   tftlcd.o
       352         26          0          2        200       2352   usart.o
       396         36          0          0          0       3010   windspeed.o

    ----------------------------------------------------------------------
      8698        <USER>      <GROUP>         36       1224     356289   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        26          0          0          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
      2308         96          0          0          0        604   printfa.o
         0          0          0          4          0          0   stdout.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o

    ----------------------------------------------------------------------
      3742        <USER>          <GROUP>          4          0       1772   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2634        112          0          4          0       1048   mc_w.l
      1104          0          0          0          0        724   mf_w.l

    ----------------------------------------------------------------------
      3742        <USER>          <GROUP>          4          0       1772   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     12440        966      19136         40       1224     349081   Grand Totals
     12440        966      19136         40       1224     349081   ELF Image Totals
     12440        966      19136         40          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                31576 (  30.84kB)
    Total RW  Size (RW Data + ZI Data)              1264 (   1.23kB)
    Total ROM Size (Code + RO Data + RW Data)      31616 (  30.88kB)

==============================================================================

