--cpu=Cortex-M4.fp.sp
".\obj\main.o"
".\obj\stm32f4xx_it.o"
".\obj\startup_stm32f40_41xxx.o"
".\obj\stm32f4xx_gpio.o"
".\obj\stm32f4xx_rcc.o"
".\obj\stm32f4xx_syscfg.o"
".\obj\misc.o"
".\obj\stm32f4xx_exti.o"
".\obj\stm32f4xx_tim.o"
".\obj\stm32f4xx_usart.o"
".\obj\stm32f4xx_iwdg.o"
".\obj\stm32f4xx_wwdg.o"
".\obj\stm32f4xx_rng.o"
".\obj\stm32f4xx_pwr.o"
".\obj\stm32f4xx_adc.o"
".\obj\stm32f4xx_dac.o"
".\obj\stm32f4xx_dma.o"
".\obj\stm32f4xx_rtc.o"
".\obj\stm32f4xx_can.o"
".\obj\stm32f4xx_fsmc.o"
".\obj\system_stm32f4xx.o"
".\obj\system.o"
".\obj\systick.o"
".\obj\usart.o"
".\obj\led.o"
".\obj\tftlcd.o"
".\obj\dht22.o"
".\obj\lcd_test.o"
".\obj\windspeed.o"
".\obj\bh1750.o"
".\obj\logo.o"
--library_type=microlib --strict --scatter ".\Obj\Template.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Obj\Template.map" -o .\Obj\Template.axf