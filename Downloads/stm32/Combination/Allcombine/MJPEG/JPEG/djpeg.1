.TH DJPEG 1 "23 November 2013"
.SH NAME
djpeg \- decompress a JPEG file to an image file
.SH SYNOPSIS
.B djpeg
[
.I options
]
[
.I filename
]
.LP
.SH DESCRIPTION
.LP
.B djpeg
decompresses the named JPEG file, or the standard input if no file is named,
and produces an image file on the standard output.  PBMPLUS (PPM/PGM), BMP,
GIF, Targa, or RLE (Utah Raster Toolkit) output format can be selected.
(RLE is supported only if the URT library is available.)
.SH OPTIONS
All switch names may be abbreviated; for example,
.B \-grayscale
may be written
.B \-gray
or
.BR \-gr .
Most of the "basic" switches can be abbreviated to as little as one letter.
Upper and lower case are equivalent (thus
.B \-BMP
is the same as
.BR \-bmp ).
British spellings are also accepted (e.g.,
.BR \-greyscale ),
though for brevity these are not mentioned below.
.PP
The basic switches are:
.TP
.BI \-colors " N"
Reduce image to at most N colors.  This reduces the number of colors used in
the output image, so that it can be displayed on a colormapped display or
stored in a colormapped file format.  For example, if you have an 8-bit
display, you'd need to reduce to 256 or fewer colors.
.TP
.BI \-quantize " N"
Same as
.BR \-colors .
.B \-colors
is the recommended name,
.B \-quantize
is provided only for backwards compatibility.
.TP
.B \-fast
Select recommended processing options for fast, low quality output.  (The
default options are chosen for highest quality output.)  Currently, this is
equivalent to \fB\-dct fast \-nosmooth \-onepass \-dither ordered\fR.
.TP
.B \-grayscale
Force gray-scale output even if JPEG file is color.  Useful for viewing on
monochrome displays; also,
.B djpeg
runs noticeably faster in this mode.
.TP
.BI \-scale " M/N"
Scale the output image by a factor M/N.  Currently supported scale factors are
M/N with all M from 1 to 16, where N is the source DCT size, which is 8 for
baseline JPEG.  If the /N part is omitted, then M specifies the DCT scaled
size to be applied on the given input.  For baseline JPEG this is equivalent
to M/8 scaling, since the source DCT size for baseline JPEG is 8.
Scaling is handy if the image is larger than your screen; also,
.B djpeg
runs much faster when scaling down the output.
.TP
.B \-bmp
Select BMP output format (Windows flavor).  8-bit colormapped format is
emitted if
.B \-colors
or
.B \-grayscale
is specified, or if the JPEG file is gray-scale; otherwise, 24-bit full-color
format is emitted.
.TP
.B \-gif
Select GIF output format.  Since GIF does not support more than 256 colors,
.B \-colors 256
is assumed (unless you specify a smaller number of colors).
.TP
.B \-os2
Select BMP output format (OS/2 1.x flavor).  8-bit colormapped format is
emitted if
.B \-colors
or
.B \-grayscale
is specified, or if the JPEG file is gray-scale; otherwise, 24-bit full-color
format is emitted.
.TP
.B \-pnm
Select PBMPLUS (PPM/PGM) output format (this is the default format).
PGM is emitted if the JPEG file is gray-scale or if
.B \-grayscale
is specified; otherwise PPM is emitted.
.TP
.B \-rle
Select RLE output format.  (Requires URT library.)
.TP
.B \-targa
Select Targa output format.  Gray-scale format is emitted if the JPEG file is
gray-scale or if
.B \-grayscale
is specified; otherwise, colormapped format is emitted if
.B \-colors
is specified; otherwise, 24-bit full-color format is emitted.
.PP
Switches for advanced users:
.TP
.B \-dct int
Use integer DCT method (default).
.TP
.B \-dct fast
Use fast integer DCT (less accurate).
.TP
.B \-dct float
Use floating-point DCT method.
The float method is very slightly more accurate than the int method, but is
much slower unless your machine has very fast floating-point hardware.  Also
note that results of the floating-point method may vary slightly across
machines, while the integer methods should give the same results everywhere.
The fast integer method is much less accurate than the other two.
.TP
.B \-dither fs
Use Floyd-Steinberg dithering in color quantization.
.TP
.B \-dither ordered
Use ordered dithering in color quantization.
.TP
.B \-dither none
Do not use dithering in color quantization.
By default, Floyd-Steinberg dithering is applied when quantizing colors; this
is slow but usually produces the best results.  Ordered dither is a compromise
between speed and quality; no dithering is fast but usually looks awful.  Note
that these switches have no effect unless color quantization is being done.
Ordered dither is only available in
.B \-onepass
mode.
.TP
.BI \-map " file"
Quantize to the colors used in the specified image file.  This is useful for
producing multiple files with identical color maps, or for forcing a
predefined set of colors to be used.  The
.I file
must be a GIF or PPM file. This option overrides
.B \-colors
and
.BR \-onepass .
.TP
.B \-nosmooth
Don't use high-quality upsampling.
.TP
.B \-onepass
Use one-pass instead of two-pass color quantization.  The one-pass method is
faster and needs less memory, but it produces a lower-quality image.
.B \-onepass
is ignored unless you also say
.B \-colors
.IR N .
Also, the one-pass method is always used for gray-scale output (the two-pass
method is no improvement then).
.TP
.BI \-maxmemory " N"
Set limit for amount of memory to use in processing large images.  Value is
in thousands of bytes, or millions of bytes if "M" is attached to the
number.  For example,
.B \-max 4m
selects 4000000 bytes.  If more space is needed, temporary files will be used.
.TP
.BI \-outfile " name"
Send output image to the named file, not to standard output.
.TP
.B \-verbose
Enable debug printout.  More
.BR \-v 's
give more output.  Also, version information is printed at startup.
.TP
.B \-debug
Same as
.BR \-verbose .
.SH EXAMPLES
.LP
This example decompresses the JPEG file foo.jpg, quantizes it to
256 colors, and saves the output in 8-bit BMP format in foo.bmp:
.IP
.B djpeg \-colors 256 \-bmp
.I foo.jpg
.B >
.I foo.bmp
.SH HINTS
To get a quick preview of an image, use the
.B \-grayscale
and/or
.B \-scale
switches.
.B \-grayscale \-scale 1/8
is the fastest case.
.PP
Several options are available that trade off image quality to gain speed.
.B \-fast
turns on the recommended settings.
.PP
.B \-dct fast
and/or
.B \-nosmooth
gain speed at a small sacrifice in quality.
When producing a color-quantized image,
.B \-onepass \-dither ordered
is fast but much lower quality than the default behavior.
.B \-dither none
may give acceptable results in two-pass mode, but is seldom tolerable in
one-pass mode.
.PP
If you are fortunate enough to have very fast floating point hardware,
\fB\-dct float\fR may be even faster than \fB\-dct fast\fR.  But on most
machines \fB\-dct float\fR is slower than \fB\-dct int\fR; in this case it is
not worth using, because its theoretical accuracy advantage is too small to be
significant in practice.
.SH ENVIRONMENT
.TP
.B JPEGMEM
If this environment variable is set, its value is the default memory limit.
The value is specified as described for the
.B \-maxmemory
switch.
.B JPEGMEM
overrides the default value specified when the program was compiled, and
itself is overridden by an explicit
.BR \-maxmemory .
.SH SEE ALSO
.BR cjpeg (1),
.BR jpegtran (1),
.BR rdjpgcom (1),
.BR wrjpgcom (1)
.br
.BR ppm (5),
.BR pgm (5)
.br
Wallace, Gregory K.  "The JPEG Still Picture Compression Standard",
Communications of the ACM, April 1991 (vol. 34, no. 4), pp. 30-44.
.SH AUTHOR
Independent JPEG Group
.SH BUGS
To avoid the Unisys LZW patent (now expired),
.B djpeg
produces uncompressed GIF files.  These are larger than they should be, but
are readable by standard GIF decoders.
