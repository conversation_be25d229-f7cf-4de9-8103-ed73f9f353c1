﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="jconfig.h" />
    <ClInclude Include="jdct.h" />
    <ClInclude Include="jerror.h" />
    <ClInclude Include="jinclude.h" />
    <ClInclude Include="jmemsys.h" />
    <ClInclude Include="jmorecfg.h" />
    <ClInclude Include="jpegint.h" />
    <ClInclude Include="jpeglib.h" />
    <ClInclude Include="jversion.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="jaricom.c" />
    <ClCompile Include="jcapimin.c" />
    <ClCompile Include="jcapistd.c" />
    <ClCompile Include="jcarith.c" />
    <ClCompile Include="jccoefct.c" />
    <ClCompile Include="jccolor.c" />
    <ClCompile Include="jcdctmgr.c" />
    <ClCompile Include="jchuff.c" />
    <ClCompile Include="jcinit.c" />
    <ClCompile Include="jcmainct.c" />
    <ClCompile Include="jcmarker.c" />
    <ClCompile Include="jcmaster.c" />
    <ClCompile Include="jcomapi.c" />
    <ClCompile Include="jcparam.c" />
    <ClCompile Include="jcprepct.c" />
    <ClCompile Include="jcsample.c" />
    <ClCompile Include="jctrans.c" />
    <ClCompile Include="jdapimin.c" />
    <ClCompile Include="jdapistd.c" />
    <ClCompile Include="jdarith.c" />
    <ClCompile Include="jdatadst.c" />
    <ClCompile Include="jdatasrc.c" />
    <ClCompile Include="jdcoefct.c" />
    <ClCompile Include="jdcolor.c" />
    <ClCompile Include="jddctmgr.c" />
    <ClCompile Include="jdhuff.c" />
    <ClCompile Include="jdinput.c" />
    <ClCompile Include="jdmainct.c" />
    <ClCompile Include="jdmarker.c" />
    <ClCompile Include="jdmaster.c" />
    <ClCompile Include="jdmerge.c" />
    <ClCompile Include="jdpostct.c" />
    <ClCompile Include="jdsample.c" />
    <ClCompile Include="jdtrans.c" />
    <ClCompile Include="jerror.c" />
    <ClCompile Include="jfdctflt.c" />
    <ClCompile Include="jfdctfst.c" />
    <ClCompile Include="jfdctint.c" />
    <ClCompile Include="jidctflt.c" />
    <ClCompile Include="jidctfst.c" />
    <ClCompile Include="jidctint.c" />
    <ClCompile Include="jmemmgr.c" />
    <ClCompile Include="jmemnobs.c" />
    <ClCompile Include="jquant1.c" />
    <ClCompile Include="jquant2.c">
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Disabled</Optimization>
      <BufferSecurityCheck Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</BufferSecurityCheck>
    </ClCompile>
    <ClCompile Include="jutils.c" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{019DBD2A-273D-4BA4-BF86-B5EFE2ED76B1}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>jpeg</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>Full</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;_CRT_SECURE_NO_WARNINGS</PreprocessorDefinitions>
      <OmitFramePointers>true</OmitFramePointers>
      <EnableFiberSafeOptimizations>true</EnableFiberSafeOptimizations>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>