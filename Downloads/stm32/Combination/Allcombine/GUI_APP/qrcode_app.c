#include "qrcode_app.h"
#include "button.h"
#include "touch.h"
#include "common.h"
#include "wm8978.h"	
#include "videoplayer.h" 
#include "time.h"
#include "key.h"
#include "font_show.h"


__videodev videodev;		//��Ƶ���ſ�����


//��Ƶ�б�
u8*const VIDEO_LIST[GUI_LANGUAGE_NUM]=
{
	"��Ƶ�б�","��Ƶ�б�","VIDEO LIST",
};

//video�ļ����,���ļ��洢����
//videodev:audio�ṹ��
//����ֵ:0,��������/�����˳���ť.
//		 1,�ڴ����ʧ��	
//		2,�˳�
u8 video_filelist(__videodev *audiodevx)
{
	u8 res;
	u8 rval=0;			//返回值
  	u16 i;
	u8 key=0;			// 添加按键变量声明
 	_btn_obj* rbtn;		//返回按钮控件
 	_btn_obj* qbtn;		//退出按钮控件

   	_filelistbox_obj * flistbox;
	_filelistbox_list * filelistx; 	//文件
 	app_filebrower((u8*)VIDEO_LIST[gui_phy.language],0X07);	//ѡ��Ŀ���ļ�,���õ�Ŀ������
 
  	flistbox=filelistbox_creat(0,gui_phy.tbheight,tftlcd_data.width,tftlcd_data.height-gui_phy.tbheight*2,1,gui_phy.listfsize);//����һ��filelistbox
 	if(flistbox==NULL)rval=1;							//�����ڴ�ʧ��.
	else if(audiodevx->path==NULL)  
	{
		flistbox->fliter=FLBOX_FLT_VIDEO;	//������Ƶ�ļ�
 		filelistbox_add_disk(flistbox);		//���Ӵ���·��
		filelistbox_draw_listbox(flistbox);
	}else
	{
		flistbox->fliter=FLBOX_FLT_VIDEO;		//������Ƶ�ļ�	 
		flistbox->path=(u8*)gui_memin_malloc(strlen((const char*)audiodevx->path)+1);//Ϊ·�������ڴ�
		strcpy((char *)flistbox->path,(char *)audiodevx->path);//����·��	    
		filelistbox_scan_filelist(flistbox);	//����ɨ���б� 
		flistbox->selindex=flistbox->foldercnt+audiodevx->curindex;//ѡ����ĿΪ��ǰ���ڲ��ŵ���Ŀ
		if(flistbox->scbv->totalitems>flistbox->scbv->itemsperpage)flistbox->scbv->topitem=flistbox->selindex;  
		filelistbox_draw_listbox(flistbox);		//�ػ� 		 
	} 	 		 
	rbtn=btn_creat(tftlcd_data.width-2*gui_phy.tbfsize-8-1,tftlcd_data.height-gui_phy.tbheight,2*gui_phy.tbfsize+8,gui_phy.tbheight-1,0,0x03);//�������ְ�ť
  	qbtn=btn_creat(0,tftlcd_data.height-gui_phy.tbheight,2*gui_phy.tbfsize+8,gui_phy.tbheight,0,0x03);//�����˳����ְ�ť
	if(rbtn==NULL||qbtn==NULL)rval=1;	//û���㹻�ڴ湻����
	else
	{
	 	rbtn->caption=(u8*)GUI_BACK_CAPTION_TBL[gui_phy.language];	//����
	 	rbtn->font=gui_phy.tbfsize;//�����µ������С	 	 
		rbtn->bcfdcolor=WHITE;	//����ʱ����ɫ
		rbtn->bcfucolor=WHITE;	//�ɿ�ʱ����ɫ
		btn_draw(rbtn);//����ť 
		
	 	qbtn->caption=(u8*)GUI_QUIT_CAPTION_TBL[gui_phy.language];	//����
	 	qbtn->font=gui_phy.tbfsize;//�����µ������С	 
		qbtn->bcfdcolor=WHITE;	//����ʱ����ɫ
		qbtn->bcfucolor=WHITE;	//�ɿ�ʱ����ɫ
		btn_draw(qbtn);//����ť
	}	   
   	while(rval==0)
	{
		tp_dev.scan(0);
		in_obj.get_key(&tp_dev,IN_TYPE_TOUCH);	//获得输入值
		delay_ms(10);		//延时一个时钟节拍

		// 增强按键支持
		key = KEY_Scan(1);
		if(key == KEY1_PRESS)
		{
			delay_ms(100); // 按键防抖延时
			break;		//KEY1退出
		}

		if(key == KEY0_PRESS) // KEY0选择/确认
		{
			delay_ms(100); // 按键防抖延时

			// 先检查当前选择的是什么类型
			filelistx=filelist_search(flistbox->list,flistbox->selindex);
			if(filelistx)
			{
				// 调试信息：显示文件类型和名称
				printf("Selected: %s, Type: %d\r\n", filelistx->name, filelistx->type);

				if(filelistx->type==FICO_VIDEO) // 如果是视频文件
				{
					printf("Playing video file: %s\r\n", filelistx->name);
					// 选择了视频文件，设置双击标志让主循环处理
					flistbox->dbclick = 0x81; // 设置双击标志，这会在主循环中被处理
					// 不要在这里break，让主循环处理双击逻辑
				}
				else if(filelistx->type==FICO_DISK || filelistx->type==FICO_FOLDER) // 如果是磁盘或文件夹
				{
					printf("Entering folder/disk: %s\r\n", filelistx->name);
					// 模拟双击操作来进入磁盘/文件夹
					flistbox->dbclick = 0x81; // 设置双击标志
					filelistbox_check_filelist(flistbox); // 调用进入磁盘/文件夹的函数
					flistbox->dbclick = 0; // 清除双击标志
				}
				else
				{
					printf("Unknown file type: %d\r\n", filelistx->type);
				}
			}
		}

		if(key == KEY2_PRESS) // KEY2向下选择
		{
			delay_ms(100); // 按键防抖延时
			if(flistbox->selindex < (flistbox->foldercnt + flistbox->filecnt - 1))
			{
				flistbox->selindex++;
				filelistbox_draw_listbox(flistbox);
			}
		}

		if(key == KEY_UP_PRESS) // KEY_UP向上选择
		{
			delay_ms(100); // 按键防抖延时
			if(flistbox->selindex > 0)
			{
				flistbox->selindex--;
				filelistbox_draw_listbox(flistbox);
			}
		}

		filelistbox_check(flistbox,&in_obj);	//扫描文件
		res=btn_check(rbtn,&in_obj);
		if(res)
		{
			if(((rbtn->sta&0X80)==0))//按键状态改变了
			{
				if(flistbox->dbclick!=0X81)
				{
 					filelistx=filelist_search(flistbox->list,flistbox->selindex);//得到当时选中的list的信息
					if(filelistx->type==FICO_DISK)//已经选择了磁盘
					{
						rval=2;
						break;//退出
					}else filelistbox_back(flistbox);//返回上一级目录
				}
 			}
		}
		res=btn_check(qbtn,&in_obj);
		if(res)
		{
			if(((qbtn->sta&0X80)==0))//��ť״̬�ı���
			{ 
				rval=2;
				break;//�˳�
 			}	 
		}   
		if(flistbox->dbclick==0X81)//˫���ļ���
		{											 
			gui_memin_free(audiodevx->path);		//�ͷ��ڴ�
			gui_memin_free(audiodevx->mfindextbl);	//�ͷ��ڴ�
			audiodevx->path=(u8*)gui_memin_malloc(strlen((const char*)flistbox->path)+1);//Ϊ�µ�·�������ڴ�
			if(audiodevx->path==NULL){rval=1;break;}
			audiodevx->path[0]='\0';//���ʼ���������.
 			strcpy((char *)audiodevx->path,(char *)flistbox->path);
			audiodevx->mfindextbl=(u16*)gui_memin_malloc(flistbox->filecnt*2);//Ϊ�µ�tbl�����ڴ�
			if(audiodevx->mfindextbl==NULL){rval=1;break;}
		    for(i=0;i<flistbox->filecnt;i++)audiodevx->mfindextbl[i]=flistbox->findextbl[i];//����
			audiodevx->mfilenum=flistbox->filecnt;		//��¼�ļ�����	
			videodev.curindex=flistbox->selindex-flistbox->foldercnt;
			flistbox->dbclick=0;
			break;	 							   			   
		}
	}	
	filelistbox_delete(flistbox);	//ɾ��filelist
	btn_delete(qbtn);				//ɾ����ť	  	 
	btn_delete(rbtn);				//ɾ����ť	   	
	if(rval)
	{
		gui_memin_free(audiodevx->path);		//�ͷ��ڴ�
		gui_memin_free(audiodevx->mfindextbl); 	//�ͷ��ڴ�
		gui_memin_free(audiodevx);
 	}	 
 	return rval; 
} 


void Qrcode_APP_Test(void)
{
	u8 rval=0;
	u8 res;	  
	u8 *pname=0; 
	u8 key; 
	FILINFO *vfileinfo;	//�ļ���Ϣ
	DIR vdir;	 		//Ŀ¼
	
	
	WM8978_Init();				//��ʼ��WM8978
	WM8978_ADDA_Cfg(1,0);		//����DAC
	WM8978_Input_Cfg(0,0,0);	//�ر�����ͨ��
	WM8978_Output_Cfg(1,0);		//����DAC���  
	WM8978_HPvol_Set(40,40);
	WM8978_SPKvol_Set(60);
	
	memset(&videodev,0,sizeof(__videodev));//videodev������������
	res=video_filelist(&videodev);//ѡ����Ƶ�ļ����в���
	if(res)	//ʧ�ܷ���������
	{
		// 注意：vfileinfo和pname在这里还没有分配内存，不要释放
		// myfree(SRAMIN,vfileinfo);			//这里不应该释放
		// myfree(SRAMIN,pname);				//这里不应该释放
		if(videodev.path) gui_memin_free(videodev.path);		//只释放已分配的内存
		if(videodev.mfindextbl) gui_memin_free(videodev.mfindextbl);//只释放已分配的内存
		WM8978_ADDA_Cfg(0,0);				//�ر�DAC&ADC
		WM8978_Input_Cfg(0,0,0);			//�ر�����ͨ��
		WM8978_Output_Cfg(0,0);				//�ر�DAC���
		WM8978_HPvol_Set(0,0);	//������������
		WM8978_SPKvol_Set(0);	//������������
		ICON_UI_Init();
		return;
	}
	
	FRONT_COLOR=WHITE;  
  	BACK_COLOR=BLACK;  
	LCD_Clear(BACK_COLOR);		//����
	app_filebrower("��Ƶ������Ӧ��",0X05);//��ʾ����
	app_gui_tcbar(0,tftlcd_data.height-gui_phy.tbheight,tftlcd_data.width,gui_phy.tbheight,0x01);	//�Ϸֽ���
	
	vfileinfo=(FILINFO*)mymalloc(SRAMIN,sizeof(FILINFO));//Ϊ���ļ������������ڴ� 
	rval=f_opendir(&vdir,(const TCHAR*)videodev.path);	//��ѡ�е�Ŀ¼
	while(rval==0&&vfileinfo)
	{
VIDEO_START:
		dir_sdi(&vdir,videodev.mfindextbl[videodev.curindex]);
		rval=f_readdir(&vdir,vfileinfo);//��ȡ�ļ���Ϣ
		if(rval!=FR_OK||vfileinfo->fname[0]==0)break;	//������/��ĩβ��,�˳�
		videodev.name=(u8*)(vfileinfo->fname); 
		pname=gui_memin_malloc(strlen((const char*)videodev.name)+strlen((const char*)videodev.path)+2);//�����ڴ�
		if(pname==NULL)break;//����ʧ��    
		pname=gui_path_name(pname,videodev.path,videodev.name);	//�ļ�������·�� 
		printf("play:%s\r\n",pname); 
		LCD_Clear(BACK_COLOR);							//������
		video_bmsg_show((u8*)vfileinfo->fname,videodev.curindex+1,videodev.mfilenum);//��ʾ����,��������Ϣ		
		gui_show_string("KEY1:������Ƶ�б�",10,120,tftlcd_data.width,16,16,RED);		 	 
		gui_show_string("KEY2:��һ��",10,140,tftlcd_data.width,16,16,RED);		 	 
		gui_show_string("KEY_UP:���",10,160,tftlcd_data.width,16,16,RED);		 	 
		
		key=video_play_mjpeg(pname); 			 	//���������Ƶ�ļ�
		if(key<=1)		//��һ��
		{
			videodev.curindex++;		   	
			if(videodev.curindex>=videodev.mfilenum)videodev.curindex=0;//��ĩβ��ʱ��,�Զ���ͷ��ʼ
 		}
		else if(key==2)
		{
			gui_memin_free(pname);
			res=video_filelist(&videodev);//ѡ����Ƶ�ļ����в���
			if(res==1||res==2)break;
			goto VIDEO_START;
		}
		else break;	//�����˴��� 
	}
	if(vfileinfo) myfree(SRAMIN,vfileinfo);			//释放内存
	if(pname) gui_memin_free(pname);				//使用正确的释放函数
	if(videodev.path) gui_memin_free(videodev.path);		//释放内存
	if(videodev.mfindextbl) gui_memin_free(videodev.mfindextbl);//释放内存
	WM8978_ADDA_Cfg(0,0);				//�ر�DAC&ADC
	WM8978_Input_Cfg(0,0,0);			//�ر�����ͨ��
	WM8978_Output_Cfg(0,0);				//�ر�DAC���
	WM8978_HPvol_Set(0,0);	//������������
	WM8978_SPKvol_Set(0);	//������������
	ICON_UI_Init();
}
