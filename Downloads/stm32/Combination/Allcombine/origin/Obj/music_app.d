.\obj\music_app.o: GUI_APP\music_app.c
.\obj\music_app.o: GUI_APP\music_app.h
.\obj\music_app.o: .\GUI\gui.h
.\obj\music_app.o: .\Public\system.h
.\obj\music_app.o: .\User\stm32f4xx.h
.\obj\music_app.o: .\Libraries\CMSIS\core_cm4.h
.\obj\music_app.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\obj\music_app.o: .\Libraries\CMSIS\core_cmInstr.h
.\obj\music_app.o: .\Libraries\CMSIS\core_cmFunc.h
.\obj\music_app.o: .\Libraries\CMSIS\core_cmSimd.h
.\obj\music_app.o: .\Libraries\CMSIS\system_stm32f4xx.h
.\obj\music_app.o: .\User\stm32f4xx_conf.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_adc.h
.\obj\music_app.o: .\User\stm32f4xx.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_crc.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dbgmcu.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dma.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_exti.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_flash.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_gpio.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_i2c.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_iwdg.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_pwr.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rcc.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rtc.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_sdio.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_spi.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_syscfg.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_tim.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_usart.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_wwdg.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\misc.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_cryp.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_hash.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rng.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_can.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dac.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dcmi.h
.\obj\music_app.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_fsmc.h
.\obj\music_app.o: .\GUI\button.h
.\obj\music_app.o: .\GUI\guix.h
.\obj\music_app.o: .\Malloc\malloc.h
.\obj\music_app.o: .\APP\tftlcd\tftlcd.h
.\obj\music_app.o: .\Public\usart.h
.\obj\music_app.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\obj\music_app.o: .\Public\SysTick.h
.\obj\music_app.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\obj\music_app.o: .\APP\time\time.h
.\obj\music_app.o: .\GUI\button.h
.\obj\music_app.o: .\Picture\piclib.h
.\obj\music_app.o: .\Fatfs\src\ff.h
.\obj\music_app.o: .\Fatfs\src\integer.h
.\obj\music_app.o: .\Fatfs\src\ffconf.h
.\obj\music_app.o: .\Fatfs\fatfs_app\fatfs_app.h
.\obj\music_app.o: .\Picture\bmp.h
.\obj\music_app.o: .\Picture\tjpgd.h
.\obj\music_app.o: .\Picture\integer.h
.\obj\music_app.o: .\Picture\gif.h
.\obj\music_app.o: .\GUI\listbox.h
.\obj\music_app.o: .\GUI\scrollbar.h
.\obj\music_app.o: .\GUI\progressbar.h
.\obj\music_app.o: .\GUI\filelistbox.h
.\obj\music_app.o: .\GUI\edit.h
.\obj\music_app.o: .\GUI\memo.h
.\obj\music_app.o: .\GUI\window.h
.\obj\music_app.o: .\APP\touch\touch.h
.\obj\music_app.o: GUI_APP\common.h
.\obj\music_app.o: .\APP\key\key.h
.\obj\music_app.o: .\APP\wm8978\wm8978.h
.\obj\music_app.o: .\AudioDecod\audioplay.h
.\obj\music_app.o: .\AudioDecod\wav\wavplay.h
