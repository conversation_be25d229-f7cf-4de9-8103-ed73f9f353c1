.\obj\ethernetif.o: LwIP\lwip-1.4.1\src\netif\ethernetif.c
.\obj\ethernetif.o: .\LwIP\lwip-1.4.1\src\include\netif/ethernetif.h
.\obj\ethernetif.o: .\LwIP\lwip-1.4.1\src\include\lwip/err.h
.\obj\ethernetif.o: .\LwIP\lwip-1.4.1\src\include\lwip/opt.h
.\obj\ethernetif.o: .\LwIP\lwip_app\lwip_comm\lwipopts.h
.\obj\ethernetif.o: .\LwIP\lwip-1.4.1\src\include\lwip/debug.h
.\obj\ethernetif.o: .\LwIP\lwip-1.4.1\src\include\lwip/arch.h
.\obj\ethernetif.o: .\LwIP\arch/cc.h
.\obj\ethernetif.o: .\LwIP\arch/cpu.h
.\obj\ethernetif.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\obj\ethernetif.o: .\LwIP\lwip-1.4.1\src\include\lwip/opt.h
.\obj\ethernetif.o: .\LwIP\lwip-1.4.1\src\include\lwip/netif.h
.\obj\ethernetif.o: .\LwIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h
.\obj\ethernetif.o: .\LwIP\lwip-1.4.1\src\include\lwip/def.h
.\obj\ethernetif.o: .\LwIP\lwip-1.4.1\src\include\lwip/pbuf.h
.\obj\ethernetif.o: .\APP\lan8720\lan8720.h
.\obj\ethernetif.o: .\Public\system.h
.\obj\ethernetif.o: .\User\stm32f4xx.h
.\obj\ethernetif.o: .\Libraries\CMSIS\core_cm4.h
.\obj\ethernetif.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\obj\ethernetif.o: .\Libraries\CMSIS\core_cmInstr.h
.\obj\ethernetif.o: .\Libraries\CMSIS\core_cmFunc.h
.\obj\ethernetif.o: .\Libraries\CMSIS\core_cmSimd.h
.\obj\ethernetif.o: .\Libraries\CMSIS\system_stm32f4xx.h
.\obj\ethernetif.o: .\User\stm32f4xx_conf.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_adc.h
.\obj\ethernetif.o: .\User\stm32f4xx.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_crc.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dbgmcu.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dma.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_exti.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_flash.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_gpio.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_i2c.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_iwdg.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_pwr.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rcc.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rtc.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_sdio.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_spi.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_syscfg.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_tim.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_usart.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_wwdg.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\misc.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_cryp.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_hash.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rng.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_can.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dac.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dcmi.h
.\obj\ethernetif.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_fsmc.h
.\obj\ethernetif.o: .\Libraries\STM32F4x7_ETH_Driver\inc\stm32f4x7_eth.h
.\obj\ethernetif.o: .\Libraries\STM32F4x7_ETH_Driver\inc\stm32f4x7_eth_conf.h
.\obj\ethernetif.o: .\LwIP\lwip_app\lwip_comm\lwip_comm.h
.\obj\ethernetif.o: .\LwIP\lwip-1.4.1\src\include\netif/etharp.h
.\obj\ethernetif.o: .\LwIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h
.\obj\ethernetif.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
