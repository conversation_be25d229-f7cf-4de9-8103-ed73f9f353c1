--cpu=Cortex-M4.fp.sp
".\obj\main.o"
".\obj\stm32f4xx_it.o"
".\obj\startup_stm32f40_41xxx.o"
".\obj\stm32f4xx_gpio.o"
".\obj\stm32f4xx_rcc.o"
".\obj\stm32f4xx_syscfg.o"
".\obj\misc.o"
".\obj\stm32f4xx_exti.o"
".\obj\stm32f4xx_tim.o"
".\obj\stm32f4xx_usart.o"
".\obj\stm32f4xx_iwdg.o"
".\obj\stm32f4xx_wwdg.o"
".\obj\stm32f4xx_rng.o"
".\obj\stm32f4xx_pwr.o"
".\obj\stm32f4xx_adc.o"
".\obj\stm32f4xx_dac.o"
".\obj\stm32f4xx_dma.o"
".\obj\stm32f4xx_rtc.o"
".\obj\stm32f4xx_can.o"
".\obj\stm32f4xx_fsmc.o"
".\obj\stm32f4xx_spi.o"
".\obj\stm32f4xx_flash.o"
".\obj\stm32f4xx_sdio.o"
".\obj\stm32f4x7_eth.o"
".\obj\stm32f4xx_dcmi.o"
".\obj\system_stm32f4xx.o"
".\obj\system.o"
".\obj\systick.o"
".\obj\usart.o"
".\obj\led.o"
".\obj\key.o"
".\obj\sram.o"
".\obj\beep.o"
".\obj\24cxx.o"
".\obj\iic.o"
".\obj\flash.o"
".\obj\spi.o"
".\obj\time.o"
".\obj\rtc.o"
".\obj\sdio_sdcard.o"
".\obj\hwjs.o"
".\obj\rs232.o"
".\obj\lan8720.o"
".\obj\tftlcd.o"
".\obj\ctiic.o"
".\obj\gt5663.o"
".\obj\touch.o"
".\obj\adc.o"
".\obj\adc_temp.o"
".\obj\ds18b20.o"
".\obj\can.o"
".\obj\rs485.o"
".\obj\touch_key.o"
".\obj\lsens.o"
".\obj\mpu6050.o"
".\obj\inv_mpu.o"
".\obj\inv_mpu_dmp_motion_driver.o"
".\obj\wm8978.o"
".\obj\nrf24l01.o"
".\obj\i2s.o"
".\obj\dcmi.o"
".\obj\ov2640.o"
".\obj\sccb.o"
".\obj\cst716.o"
".\obj\malloc.o"
".\obj\diskio.o"
".\obj\ff.o"
".\obj\fatfs_app.o"
".\obj\mycc936.o"
".\obj\font_show.o"
".\obj\font_update.o"
".\obj\bmp.o"
".\obj\gif.o"
".\obj\piclib.o"
".\obj\tjpgd.o"
".\obj\pyinput.o"
".\obj\t9input.o"
".\obj\audioplay.o"
".\obj\wavplay.o"
".\obj\videoplayer.o"
".\obj\avi.o"
".\obj\mjpeg.o"
".\obj\jaricom.o"
".\obj\jcomapi.o"
".\obj\jutils.o"
".\obj\jerror.o"
".\obj\jmemmgr.o"
".\obj\jdapimin.o"
".\obj\jdapistd.o"
".\obj\jdarith.o"
".\obj\jdmaster.o"
".\obj\jdinput.o"
".\obj\jdmarker.o"
".\obj\jdhuff.o"
".\obj\jdmainct.o"
".\obj\jdcoefct.o"
".\obj\jdpostct.o"
".\obj\jddctmgr.o"
".\obj\jidctfst.o"
".\obj\jidctflt.o"
".\obj\jdsample.o"
".\obj\jdcolor.o"
".\obj\jquant1.o"
".\obj\jquant2.o"
".\obj\jdmerge.o"
".\obj\jmemnobs.o"
".\obj\wrppm.o"
".\obj\jidctint.o"
".\obj\lwip_comm.o"
".\obj\fs.o"
".\obj\httpd.o"
".\obj\httpd_cgi_ssi.o"
".\obj\etharp.o"
".\obj\ethernetif.o"
".\obj\autoip.o"
".\obj\icmp.o"
".\obj\igmp.o"
".\obj\inet.o"
".\obj\inet_chksum.o"
".\obj\ip.o"
".\obj\ip_addr.o"
".\obj\ip_frag.o"
".\obj\def.o"
".\obj\dhcp.o"
".\obj\dns.o"
".\obj\init.o"
".\obj\lwip_sys.o"
".\obj\mem.o"
".\obj\memp.o"
".\obj\netif.o"
".\obj\pbuf.o"
".\obj\raw.o"
".\obj\stats.o"
".\obj\tcp.o"
".\obj\tcp_in.o"
".\obj\tcp_out.o"
".\obj\timers.o"
".\obj\udp.o"
".\obj\sys_arch.o"
".\obj\api_lib.o"
".\obj\api_msg.o"
".\obj\err.o"
".\obj\netbuf.o"
".\obj\netdb.o"
".\obj\netifapi.o"
".\obj\sockets.o"
".\obj\tcpip.o"
".\obj\usb_core.o"
".\obj\usb_dcd.o"
".\obj\usb_dcd_int.o"
".\obj\usbd_core.o"
".\obj\usbd_ioreq.o"
".\obj\usbd_req.o"
".\obj\usbd_msc_bot.o"
".\obj\usbd_msc_core.o"
".\obj\usbd_msc_data.o"
".\obj\usbd_msc_scsi.o"
".\obj\usb_bsp.o"
".\obj\usbd_desc.o"
".\obj\usbd_storage_msd.o"
".\obj\usbd_usr.o"
".\obj\button.o"
".\obj\edit.o"
".\obj\filelistbox.o"
".\obj\gui.o"
".\obj\listbox.o"
".\obj\memo.o"
".\obj\progressbar.o"
".\obj\scrollbar.o"
".\obj\window.o"
".\obj\common.o"
".\obj\led_app.o"
".\obj\rtc_app.o"
".\obj\calculator_app.o"
".\obj\gyroscope_app.o"
".\obj\picture_app.o"
".\obj\ebook_app.o"
".\obj\notepad_app.o"
".\obj\paint_app.o"
".\obj\com_app.o"
".\obj\phone_app.o"
".\obj\qrcode_app.o"
".\obj\wireless_app.o"
".\obj\music_app.o"
".\obj\usb_app.o"
".\obj\eth_app.o"
".\obj\camera_app.o"
--library_type=microlib --strict --scatter ".\Obj\Template.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Obj\Template.map" -o .\Obj\Template.axf