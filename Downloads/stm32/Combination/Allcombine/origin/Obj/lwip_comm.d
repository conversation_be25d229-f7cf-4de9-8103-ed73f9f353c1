.\obj\lwip_comm.o: LwIP\lwip_app\lwip_comm\lwip_comm.c
.\obj\lwip_comm.o: LwIP\lwip_app\lwip_comm\lwip_comm.h
.\obj\lwip_comm.o: .\APP\lan8720\lan8720.h
.\obj\lwip_comm.o: .\Public\system.h
.\obj\lwip_comm.o: .\User\stm32f4xx.h
.\obj\lwip_comm.o: .\Libraries\CMSIS\core_cm4.h
.\obj\lwip_comm.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\obj\lwip_comm.o: .\Libraries\CMSIS\core_cmInstr.h
.\obj\lwip_comm.o: .\Libraries\CMSIS\core_cmFunc.h
.\obj\lwip_comm.o: .\Libraries\CMSIS\core_cmSimd.h
.\obj\lwip_comm.o: .\Libraries\CMSIS\system_stm32f4xx.h
.\obj\lwip_comm.o: .\User\stm32f4xx_conf.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_adc.h
.\obj\lwip_comm.o: .\User\stm32f4xx.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_crc.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dbgmcu.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dma.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_exti.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_flash.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_gpio.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_i2c.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_iwdg.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_pwr.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rcc.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rtc.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_sdio.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_spi.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_syscfg.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_tim.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_usart.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_wwdg.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\misc.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_cryp.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_hash.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rng.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_can.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dac.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dcmi.h
.\obj\lwip_comm.o: .\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_fsmc.h
.\obj\lwip_comm.o: .\Libraries\STM32F4x7_ETH_Driver\inc\stm32f4x7_eth.h
.\obj\lwip_comm.o: .\Libraries\STM32F4x7_ETH_Driver\inc\stm32f4x7_eth_conf.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\netif/etharp.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/opt.h
.\obj\lwip_comm.o: .\LwIP\lwip_app\lwip_comm\lwipopts.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/debug.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/arch.h
.\obj\lwip_comm.o: .\LwIP\arch/cc.h
.\obj\lwip_comm.o: .\LwIP\arch/cpu.h
.\obj\lwip_comm.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/opt.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/pbuf.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/err.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/def.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/netif.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/dhcp.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/mem.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/memp.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/memp_std.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/init.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\netif\ethernetif.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/timers.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/tcp_impl.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/tcp.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\ipv4\lwip/icmp.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\ipv4\lwip/ip_frag.h
.\obj\lwip_comm.o: .\LwIP\lwip-1.4.1\src\include\lwip/tcpip.h
.\obj\lwip_comm.o: .\Malloc\malloc.h
.\obj\lwip_comm.o: .\Public\SysTick.h
.\obj\lwip_comm.o: .\Public\usart.h
