Daily Use Guide for using Savannah for lwIP

Table of Contents:

1 - Obtaining lwIP from the CVS repository
2 - Committers/developers CVS access using SSH (to be written)
3 - Merging from DEVEL branch to main trunk (stable branch)
4 - How to release lwIP



1 Obtaining lwIP from the CVS repository
----------------------------------------

To perform an anonymous CVS checkout of the main trunk (this is where
bug fixes and incremental enhancements occur), do this:

cvs -z3 -d:pserver:<EMAIL>:/sources/lwip checkout lwip
 
Or, obtain a stable branch (updated with bug fixes only) as follows:
cvs -z3 -d:pserver:<EMAIL>:/sources/lwip checkout \
  -r STABLE-0_7 -d lwip-0.7 lwip

Or, obtain a specific (fixed) release as follows:
cvs -z3 -d:pserver:<EMAIL>:/sources/lwip checkout \
  -r STABLE-0_7_0 -d lwip-0.7.0 lwip

3 Committers/developers CVS access using SSH
--------------------------------------------

The Savannah server uses SSH (Secure Shell) protocol 2 authentication and encryption.
As such, CVS commits to the server occur through a SSH tunnel for project members.
To create a SSH2 key pair in UNIX-like environments, do this:

ssh-keygen -t dsa

Under Windows, a recommended SSH client is "PuTTY", freely available with good
documentation and a graphic user interface. Use its key generator.

Now paste the id_dsa.pub contents into your Savannah account public key list. Wait
a while so that Savannah can update its configuration (This can take minutes).

Try to login using SSH:

ssh -v <EMAIL>

If it tells you:

Authenticating with public key "your_key_name"...
Server refused to allocate pty

then you could login; Savannah refuses to give you a shell - which is OK, as we
are allowed to use SSH for CVS only. Now, you should be able to do this:

export CVS_RSH=ssh
cvs -z3 -d:ext:<EMAIL>:/sources/lwip co lwip
 
after which you can edit your local files with bug fixes or new features and
commit them. Make sure you know what you are doing when using CVS to make
changes on the repository. If in doubt, ask on the lwip-members mailing list.

(If SSH asks about authenticity of the host, you can check the key
 fingerprint against http://savannah.nongnu.org/cvs/?group=lwip)


3 Merging from DEVEL branch to main trunk (stable)
--------------------------------------------------

Merging is a delicate process in CVS and requires the
following disciplined steps in order to prevent conflicts
in the future. Conflicts can be hard to solve!

Merging from branch A to branch B requires that the A branch
has a tag indicating the previous merger. This tag is called
'merged_from_A_to_B'. After merging, the tag is moved in the
A branch to remember this merger for future merge actions.

IMPORTANT: AFTER COMMITTING A SUCCESFUL MERGE IN THE
REPOSITORY, THE TAG MUST BE SET ON THE SOURCE BRANCH OF THE
MERGE ACTION (REPLACING EXISTING TAGS WITH THE SAME NAME).

Merge all changes in DEVEL since our last merge to main:

In the working copy of the main trunk:
cvs update -P -jmerged_from_DEVEL_to_main -jDEVEL 

(This will apply the changes between 'merged_from_DEVEL_to_main'
and 'DEVEL' to your work set of files)

We can now commit the merge result.
cvs commit -R -m "Merged from DEVEL to main." 

If this worked out OK, we now move the tag in the DEVEL branch
to this merge point, so we can use this point for future merges:

cvs rtag -F -r DEVEL merged_from_DEVEL_to_main lwip 

4 How to release lwIP
---------------------

First, checkout a clean copy of the branch to be released. Tag this set with
tag name "STABLE-0_6_3". (I use release number 0.6.3 throughout this example).

Login CVS using pserver authentication, then export a clean copy of the
tagged tree. Export is similar to a checkout, except that the CVS metadata
is not created locally. 

export CVS_RSH=ssh
cvs -z3 -d:pserver:<EMAIL>:/sources/lwip checkout \
  -r STABLE-0_6_3 -d lwip-0.6.3 lwip

Archive this directory using tar, gzip'd, bzip2'd and zip'd.

tar czvf lwip-0.6.3.tar.gz lwip-0.6.3
tar cjvf lwip-0.6.3.tar.bz2 lwip-0.6.3
zip -r lwip-0.6.3.zip lwip-0.6.3

Now, sign the archives with a detached GPG binary signature as follows:

gpg -b lwip-0.6.3.tar.gz
gpg -b lwip-0.6.3.tar.bz2
gpg -b lwip-0.6.3.zip

Upload these files using anonymous FTP:
ncftp ftp://savannah.gnu.org/incoming/savannah/lwip

ncftp>mput *0.6.3.*

Additionally, you may post a news item on Savannah, like this:

A new 0.6.3 release is now available here:
http://savannah.nongnu.org/files/?group=lwip&highlight=0.6.3

You will have to submit this via the user News interface, then approve
this via the Administrator News interface.