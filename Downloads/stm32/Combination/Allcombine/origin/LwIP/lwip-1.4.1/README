INTRODUCTION

lwIP is a small independent implementation of the TCP/IP protocol
suite that has been developed by <PERSON> at the Computer and
Networks Architectures (CNA) lab at the Swedish Institute of Computer
Science (SICS).

The focus of the lwIP TCP/IP implementation is to reduce the RAM usage
while still having a full scale TCP. This making lwIP suitable for use
in embedded systems with tens of kilobytes of free RAM and room for
around 40 kilobytes of code ROM.

FEATURES

  * IP (Internet Protocol) including packet forwarding over multiple network
    interfaces
  * ICMP (Internet Control Message Protocol) for network maintenance and debugging
  * IGMP (Internet Group Management Protocol) for multicast traffic management
  * UDP (User Datagram Protocol) including experimental UDP-lite extensions
  * TCP (Transmission Control Protocol) with congestion control, RTT estimation
    and fast recovery/fast retransmit
  * Specialized raw/native API for enhanced performance
  * Optional Berkeley-like socket API
  * DNS (Domain names resolver)
  * SNMP (Simple Network Management Protocol)
  * DHCP (Dynamic Host Configuration Protocol)
  * AUTOIP (for IPv4, conform with RFC 3927)
  * PPP (Point-to-Point Protocol)
  * ARP (Address Resolution Protocol) for Ethernet

LICENSE

lwIP is freely available under a BSD license.

DEVELOPMENT

lwIP has grown into an excellent TCP/IP stack for embedded devices,
and developers using the stack often submit bug fixes, improvements,
and additions to the stack to further increase its usefulness.

Development of lwIP is hosted on Savannah, a central point for
software development, maintenance and distribution. Everyone can
help improve lwIP by use of Savannah's interface, CVS and the
mailing list. A core team of developers will commit changes to the
CVS source tree.

The lwIP TCP/IP stack is maintained in the 'lwip' CVS module and
contributions (such as platform ports) are in the 'contrib' module.

See doc/savannah.txt for details on CVS server access for users and
developers.

Last night's CVS tar ball can be downloaded from:
  http://savannah.gnu.org/cvs.backups/lwip.tar.gz [CHANGED - NEEDS FIXING]

The current CVS trees are web-browsable:
  http://savannah.nongnu.org/cgi-bin/viewcvs/lwip/lwip/
  http://savannah.nongnu.org/cgi-bin/viewcvs/lwip/contrib/

Submit patches and bugs via the lwIP project page:
  http://savannah.nongnu.org/projects/lwip/


DOCUMENTATION

The original out-dated homepage of lwIP and Adam Dunkels' papers on
lwIP are at the official lwIP home page:
  http://www.sics.se/~adam/lwip/

Self documentation of the source code is regularly extracted from the
current CVS sources and is available from this web page:
  http://www.nongnu.org/lwip/

There is now a constantly growin wiki about lwIP at
  http://lwip.wikia.com/wiki/LwIP_Wiki

Also, there are mailing lists you can subscribe at
  http://savannah.nongnu.org/mail/?group=lwip
plus searchable archives:
  http://lists.nongnu.org/archive/html/lwip-users/
  http://lists.nongnu.org/archive/html/lwip-devel/

Reading Adam's papers, the files in docs/, browsing the source code
documentation and browsing the mailing list archives is a good way to
become familiar with the design of lwIP.

Adam Dunkels <<EMAIL>>
Leon Woestenberg <<EMAIL>>

