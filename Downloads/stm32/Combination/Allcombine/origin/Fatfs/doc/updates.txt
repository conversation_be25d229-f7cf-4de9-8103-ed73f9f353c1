R0.10a, Jan 15,'14
  Added arbitrary strings as drive number in the path name. (_STR_VOLUME_ID)
  Added a configuration option of minimum sector size. (_MIN_SS)
  2nd argument of f_rename() can have a drive number and it will be ignored.
  Fixed f_mount() with forced mount fails when drive number is >= 1.
  Fixed f_close() invalidates the file object without volume lock.
  Fixed f_closedir() returns but the volume lock is left acquired.
  Fixed creation of an entry with LFN fails on too many SFN collisions.

R0.10, Oct 02,'13
  Added selection of character encoding on the file. (_STRF_ENCODE)
  Added f_closedir().
  Added forced full FAT scan for f_getfree(). (_FS_NOFSINFO)
  Added forced mount feature with changes of f_mount().
  Improved behavior of volume auto detection.
  Improved write throughput of f_puts() and f_printf().
  Changed argument of f_chdrive(), f_mkfs(), disk_read() and disk_write().
  Fixed f_write() can be truncated when the file size is close to 4GB.
  Fixed f_open(), f_mkdir() and f_setlabel() can return incorrect error code.

R0.09b, Jan 24, 2013
  Added f_getlabel() and f_setlabel(). (_USE_LABEL = 1)

R0.09a, Aug 27, 2012
  Fixed assertion failure due to OS/2 EA on FAT12/16.
  Changed API rejects null object pointer to avoid crash.
  Changed option name _FS_SHARE to _FS_LOCK.

R0.09, Sep 06, 2011
  f_mkfs() supports multiple partition to finish the multiple partition feature.
  Added f_fdisk(). (_MULTI_PARTITION = 2)

R0.08b, Jan 15, 2011
  Fast seek feature is also applied to f_read() and f_write().
  f_lseek() reports required table size on creating CLMP.
  Extended format syntax of f_printf().
  Ignores duplicated directory separators in given path names.

R0.08a, Aug 16, 2010
  Added f_getcwd(). (_FS_RPATH = 2)
  Added sector erase feature. (_USE_ERASE)
  Moved file lock semaphore table from fs object to the bss.
  Fixed a wrong directory entry is created on non-LFN cfg when the given name contains ';'.
  Fixed f_mkfs() creates wrong FAT32 volume.

R0.08, May 15, 2010
  Added a memory configuration option. (_USE_LFN)
  Added file lock feature. (_FS_SHARE)
  Added fast seek feature. (_USE_FASTSEEK)
  Changed some types on the API, XCHAR->TCHAR.
  Changed fname member in the FILINFO structure on Unicode cfg.
  String functions support UTF-8 encoding files on Unicode cfg.

R0.07e, Nov 3, 2009
  Separated out configuration options from ff.h to ffconf.h.
  Added a configuration option, _LFN_UNICODE.
  Fixed f_unlink() fails to remove a sub-dir on _FS_RPATH.
  Fixed name matching error on the 13 char boundary.
  Changed f_readdir() to return the SFN with always upper case on non-LFN cfg.

R0.07c, Jun 21, 2009
  Fixed f_unlink() may return FR_OK on error.
  Fixed wrong cache control in f_lseek().
  Added relative path feature.
  Added f_chdir().
  Added f_chdrive().
  Added proper case conversion to extended characters.

R0.07a, Apr 14, 2009
  Separated out OS dependent code on re-entrant configuration.
  Added multiple sector size support.

R0.07, Apr 01, 2009
  Merged Tiny-FatFs into FatFs as a buffer configuration option.
  Added long file name support.
  Added multiple code page support.
  Added re-entrancy for multitask operation.
  Added auto cluster size selection to f_mkfs().
  Added rewind option to f_readdir().
  Changed result code of critical errors.
  Renamed string functions to avoid name collision.

R0.06, Apr 01, 2008
  Added f_forward. (Tiny-FatFs)
  Added string functions: fgets, fputc, fputs and fprintf.
  Improved performance of f_lseek on moving to the same or following cluster.

R0.05a, Feb 03, 2008
  Added f_truncate.
  Added f_utime.
  Fixed off by one error at FAT sub-type determination.
  Fixed btr in f_read can be mistruncated.
  Fixed cached sector is left not flushed when create and close without write.

R0.05, Aug 26, 2007
  Changed arguments of f_read, f_write.
  Changed arguments of f_mkfs. (FatFs)
  Fixed f_mkfs on FAT32 creates incorrect FSInfo. (FatFs)
  Fixed f_mkdir on FAT32 creates incorrect directory. (FatFs)

R0.04b, May 05, 2007
  Added _USE_NTFLAG option.
  Added FSInfo support.
  Fixed some problems corresponds to FAT32. (Tiny-FatFs)
  Fixed DBCS name can result FR_INVALID_NAME.
  Fixed short seek (<= csize) collapses the file object.

R0.04a, Apr 01, 2007
  Supported multiple partitions on a plysical drive. (FatFs)
  Added minimization level 3.
  Added a capability of extending file size to f_lseek.
  Fixed an endian sensitive code in f_mkfs. (FatFs)
  Fixed a problem corresponds to FAT32 support. (Tiny-FatFs)

R0.04, Feb 04, 2007
  Supported multiple drive system. (FatFs)
  Changed some APIs for multiple drive system.
  Added f_mkfs. (FatFs)
  Added _USE_FAT32 option. (Tiny-FatFs)

R0.03a, Dec 11, 2006
  Improved cluster scan algolithm to write files fast.
  Fixed f_mkdir creates incorrect directory on FAT32.

R0.03, Sep 22, 2006
  Added f_rename.
  Changed option _FS_MINIMUM to _FS_MINIMIZE.

R0.02a, Jun 10, 2006
  Added a configuration option _FS_MINIMUM.

R0.02, Jun 01, 2006
  Added FAT12.
  Removed unbuffered mode.
  Fixed a problem on small (<32M) patition.

R0.01, Apr 29, 2006
  First release

R0.00, Feb 26, 2006
  Prototype (not released)

