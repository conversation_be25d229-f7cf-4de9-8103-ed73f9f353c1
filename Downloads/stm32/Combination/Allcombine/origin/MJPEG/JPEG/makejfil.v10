﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="jconfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jdct.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jerror.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jinclude.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jmemsys.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jmorecfg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpegint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpeglib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jversion.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="jaricom.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jcapimin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jcapistd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jcarith.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jccoefct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jccolor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jcdctmgr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jchuff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jcinit.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jcmainct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jcmarker.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jcmaster.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jcomapi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jcparam.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jcprepct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jcsample.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jctrans.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdapimin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdapistd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdarith.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdatadst.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdatasrc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdcoefct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdcolor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jddctmgr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdhuff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdinput.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdmainct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdmarker.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdmaster.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdmerge.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdpostct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdsample.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jdtrans.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jerror.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jfdctflt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jfdctfst.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jfdctint.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jidctflt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jidctfst.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jidctint.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jmemmgr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jmemnobs.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jquant1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jquant2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jutils.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>