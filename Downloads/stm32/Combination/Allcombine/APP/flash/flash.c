#include "flash.h"
//#include "spi.h" //SPI module removed for video player
#include "SysTick.h"
#include "usart.h"

//EN25QXX系列器件列表	   
//EN25Q80  ID  0X1314
//EN25Q16  ID  0X1315
//EN25Q32  ID  0X1316	
//EN25Q64  ID  0X1317	
//EN25Q128 ID  0X1318	
u16 EN25QXX_TYPE=0;	//定义EN25QXX芯片型号		   

//初始化SPI FLASH的IO口
void EN25QXX_Init(void)
{
	//SPI Flash removed for video player - not needed
	EN25QXX_TYPE = 0; // No external flash
	printf("EN25QXX: External SPI Flash disabled for video player\r\n");
}  

//读取EN25QXX的状态寄存器
//BIT7  6   5   4   3   2   1   0
//SPR   RV  TB BP2 BP1 BP0 WEL BUSY
//SPR:默认0,状态寄存器保护位,配合WP使用
//TB,BP2,BP1,BP0:FLASH区域写保护设置
//WEL:写使能锁定
//BUSY:忙标记位(1,忙;0,空闲)
//默认:0x00
u8 EN25QXX_ReadSR(void)   
{  
	//SPI Flash removed for video player
	return 0;   
}

//写EN25QXX状态寄存器
//只有SPR,TB,BP2,BP1,BP0(bit 7,5,4,3,2)可以写!!!
void EN25QXX_Write_SR(u8 sr)   
{   
	//SPI Flash removed for video player
}

//EN25QXX写使能	
//将WEL置位   
void EN25QXX_Write_Enable(void)   
{
	//SPI Flash removed for video player
} 

//EN25QXX写禁止	
//将WEL清零  
void EN25QXX_Write_Disable(void)   
{  
	//SPI Flash removed for video player
} 

//读取芯片ID
//返回值如下:				   
//0XEF13,表示芯片型号为W25Q80  
//0XEF14,表示芯片型号为W25Q16    
//0XEF15,表示芯片型号为W25Q32  
//0XEF16,表示芯片型号为W25Q64 
//0XEF17,表示芯片型号为W25Q128 	  
u16 EN25QXX_ReadID(void)
{
	//SPI Flash removed for video player
	return 0;
}   		    

//读取SPI FLASH  
//在指定地址开始读取指定长度的数据
//pBuffer:数据存储区
//ReadAddr:开始读取的地址(24bit)
//NumByteToRead:要读取的字节数(最大65535)
void EN25QXX_Read(u8* pBuffer,u32 ReadAddr,u16 NumByteToRead)
{
	//SPI Flash removed for video player
	// Fill buffer with zeros
	u16 i;
	for(i = 0; i < NumByteToRead; i++)
	{
		pBuffer[i] = 0;
	}
}

//SPI在一页(0~65535)内写入少于256个字节的数据
//在指定地址开始写入最大256字节的数据
//pBuffer:数据存储区
//WriteAddr:开始写入的地址(24bit)
//NumByteToWrite:要写入的字节数(最大256),该数不应该超过该页的剩余字节数!!!	 
void EN25QXX_Write_Page(u8* pBuffer,u32 WriteAddr,u16 NumByteToWrite)
{
	//SPI Flash removed for video player
}

//无检验写SPI FLASH 
//必须确保所写的地址范围内的数据全部为0XFF,否则在非0XFF处写入的数据将失败!
//具有自动换页功能 
//在指定地址开始写入指定长度的数据,但是要确保地址不越界!
//pBuffer:数据存储区
//WriteAddr:开始写入的地址(24bit)
//NumByteToWrite:要写入的字节数(最大65535)
//CHECK OK
void EN25QXX_Write_NoCheck(u8* pBuffer,u32 WriteAddr,u16 NumByteToWrite)   
{ 			 		 
	//SPI Flash removed for video player
}

//写SPI FLASH  
//在指定地址开始写入指定长度的数据
//该函数带擦除操作!
//pBuffer:数据存储区
//WriteAddr:开始写入的地址(24bit)						
//NumByteToWrite:要写入的字节数(最大65535)   
u8 EN25QXX_BUFFER[4096];		 
void EN25QXX_Write(u8* pBuffer,u32 WriteAddr,u16 NumByteToWrite)   
{ 
	//SPI Flash removed for video player
}

//擦除整个芯片		  
//等待时间超长...
void EN25QXX_Erase_Chip(void)   
{                                   
	//SPI Flash removed for video player
}

//擦除一个扇区
//Dst_Addr:扇区地址 根据实际容量设置
//擦除一个扇区的最少时间:150ms
void EN25QXX_Erase_Sector(u32 Dst_Addr)   
{  
	//SPI Flash removed for video player
}

//等待空闲
void EN25QXX_Wait_Busy(void)   
{   
	//SPI Flash removed for video player
}  

//进入掉电模式
void EN25QXX_PowerDown(void)   
{ 
	//SPI Flash removed for video player
}   

//唤醒
void EN25QXX_WAKEUP(void)   
{  
	//SPI Flash removed for video player
}   
